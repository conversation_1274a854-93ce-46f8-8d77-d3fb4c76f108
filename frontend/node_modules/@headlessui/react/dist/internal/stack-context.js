import d,{createContext as c,useContext as m}from"react";import{useEvent as p}from'../hooks/use-event.js';import{useIsoMorphicEffect as f}from'../hooks/use-iso-morphic-effect.js';let a=c(()=>{});a.displayName="StackContext";var s=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(s||{});function x(){return m(a)}function b({children:i,onUpdate:r,type:e,element:n,enabled:u}){let l=x(),o=p((...t)=>{r==null||r(...t),l(...t)});return f(()=>{let t=u===void 0||u===!0;return t&&o(0,e,n),()=>{t&&o(1,e,n)}},[o,e,n,u]),d.createElement(a.Provider,{value:o},i)}export{s as StackMessage,b as StackProvider,x as useStackContext};
