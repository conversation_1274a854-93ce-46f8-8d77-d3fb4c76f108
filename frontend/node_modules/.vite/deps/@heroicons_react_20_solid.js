import {
  require_react
} from "./chunk-3TFVT2CW.js";
import {
  __toESM
} from "./chunk-4MBMRILA.js";

// node_modules/@heroicons/react/20/solid/esm/AcademicCapIcon.js
var React = __toESM(require_react(), 1);
function AcademicCapIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React.createElement("title", {
    id: titleId
  }, title) : null, React.createElement("path", {
    fillRule: "evenodd",
    d: "M9.664 1.319a.75.75 0 0 1 .672 0 41.059 41.059 0 0 1 8.198 5.424.75.75 0 0 1-.254 1.285 31.372 31.372 0 0 0-7.86 3.83.75.75 0 0 1-.84 0 31.508 31.508 0 0 0-2.08-1.287V9.394c0-.244.116-.463.302-.592a35.504 35.504 0 0 1 3.305-2.033.75.75 0 0 0-.714-1.319 37 37 0 0 0-3.446 2.12A2.216 2.216 0 0 0 6 9.393v.38a31.293 31.293 0 0 0-4.28-1.746.75.75 0 0 1-.254-1.285 41.059 41.059 0 0 1 8.198-5.424ZM6 11.459a29.848 29.848 0 0 0-2.455-1.158 41.029 41.029 0 0 0-.39 *********** 0 0 0 .419.74c.528.256 1.046.53 1.554.82-.21.324-.455.63-.739.914a.75.75 0 1 0 1.06 1.06c.37-.369.69-.77.96-1.193a26.61 26.61 0 0 1 3.095 2.348.75.75 0 0 0 .992 0 26.547 26.547 0 0 1 5.93-********** 0 0 0 .42-.739 41.053 41.053 0 0 0-.39-3.114 29.925 29.925 0 0 0-5.199 2.801 2.25 2.25 0 0 1-2.514 0c-.41-.275-.826-.541-1.25-.797a6.985 6.985 0 0 1-1.084 3.45 26.503 26.503 0 0 0-1.281-.78A5.487 5.487 0 0 0 6 12v-.54Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef = React.forwardRef(AcademicCapIcon);
var AcademicCapIcon_default = ForwardRef;

// node_modules/@heroicons/react/20/solid/esm/AdjustmentsHorizontalIcon.js
var React2 = __toESM(require_react(), 1);
function AdjustmentsHorizontalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React2.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React2.createElement("title", {
    id: titleId
  }, title) : null, React2.createElement("path", {
    d: "M10 3.75a2 2 0 1 0-4 0 2 2 0 0 0 4 0ZM17.25 4.5a.75.75 0 0 0 0-1.5h-5.5a.75.75 0 0 0 0 1.5h5.5ZM5 3.75a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5a.75.75 0 0 1 .75.75ZM4.25 17a.75.75 0 0 0 0-1.5h-1.5a.75.75 0 0 0 0 1.5h1.5ZM17.25 17a.75.75 0 0 0 0-1.5h-5.5a.75.75 0 0 0 0 1.5h5.5ZM9 10a.75.75 0 0 1-.75.75h-5.5a.75.75 0 0 1 0-1.5h5.5A.75.75 0 0 1 9 10ZM17.25 10.75a.75.75 0 0 0 0-1.5h-1.5a.75.75 0 0 0 0 1.5h1.5ZM14 10a2 2 0 1 0-4 0 2 2 0 0 0 4 0ZM10 16.25a2 2 0 1 0-4 0 2 2 0 0 0 4 0Z"
  }));
}
var ForwardRef2 = React2.forwardRef(AdjustmentsHorizontalIcon);
var AdjustmentsHorizontalIcon_default = ForwardRef2;

// node_modules/@heroicons/react/20/solid/esm/AdjustmentsVerticalIcon.js
var React3 = __toESM(require_react(), 1);
function AdjustmentsVerticalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React3.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React3.createElement("title", {
    id: titleId
  }, title) : null, React3.createElement("path", {
    d: "M17 2.75a.75.75 0 0 0-1.5 0v5.5a.75.75 0 0 0 1.5 0v-5.5ZM17 15.75a.75.75 0 0 0-1.5 0v1.5a.75.75 0 0 0 1.5 0v-1.5ZM3.75 15a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5a.75.75 0 0 1 .75-.75ZM4.5 2.75a.75.75 0 0 0-1.5 0v5.5a.75.75 0 0 0 1.5 0v-5.5ZM10 11a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-1.5 0v-5.5A.75.75 0 0 1 10 11ZM10.75 2.75a.75.75 0 0 0-1.5 0v1.5a.75.75 0 0 0 1.5 0v-1.5ZM10 6a2 2 0 1 0 0 4 2 2 0 0 0 0-4ZM3.75 10a2 2 0 1 0 0 4 2 2 0 0 0 0-4ZM16.25 10a2 2 0 1 0 0 4 2 2 0 0 0 0-4Z"
  }));
}
var ForwardRef3 = React3.forwardRef(AdjustmentsVerticalIcon);
var AdjustmentsVerticalIcon_default = ForwardRef3;

// node_modules/@heroicons/react/20/solid/esm/ArchiveBoxArrowDownIcon.js
var React4 = __toESM(require_react(), 1);
function ArchiveBoxArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React4.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React4.createElement("title", {
    id: titleId
  }, title) : null, React4.createElement("path", {
    fillRule: "evenodd",
    d: "M2 3a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H2Zm0 4.5h16l-.811 7.71a2 2 0 0 1-1.99 1.79H4.802a2 2 0 0 1-1.99-1.79L2 7.5ZM10 9a.75.75 0 0 1 .75.75v2.546l.943-1.048a.75.75 0 1 1 1.114 1.004l-2.25 2.5a.75.75 0 0 1-1.114 0l-2.25-2.5a.75.75 0 1 1 1.114-1.004l.943 1.048V9.75A.75.75 0 0 1 10 9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef4 = React4.forwardRef(ArchiveBoxArrowDownIcon);
var ArchiveBoxArrowDownIcon_default = ForwardRef4;

// node_modules/@heroicons/react/20/solid/esm/ArchiveBoxXMarkIcon.js
var React5 = __toESM(require_react(), 1);
function ArchiveBoxXMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React5.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React5.createElement("title", {
    id: titleId
  }, title) : null, React5.createElement("path", {
    d: "M2 3a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H2Z"
  }), React5.createElement("path", {
    fillRule: "evenodd",
    d: "M2 7.5h16l-.811 7.71a2 2 0 0 1-1.99 1.79H4.802a2 2 0 0 1-1.99-1.79L2 7.5Zm5.22 1.72a.75.75 0 0 1 1.06 0L10 10.94l1.72-1.72a.75.75 0 1 1 1.06 1.06L11.06 12l1.72 1.72a.75.75 0 1 1-1.06 1.06L10 13.06l-1.72 1.72a.75.75 0 0 1-1.06-1.06L8.94 12l-1.72-1.72a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef5 = React5.forwardRef(ArchiveBoxXMarkIcon);
var ArchiveBoxXMarkIcon_default = ForwardRef5;

// node_modules/@heroicons/react/20/solid/esm/ArchiveBoxIcon.js
var React6 = __toESM(require_react(), 1);
function ArchiveBoxIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React6.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React6.createElement("title", {
    id: titleId
  }, title) : null, React6.createElement("path", {
    d: "M2 3a1 1 0 0 0-1 1v1a1 1 0 0 0 1 1h16a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1H2Z"
  }), React6.createElement("path", {
    fillRule: "evenodd",
    d: "M2 7.5h16l-.811 7.71a2 2 0 0 1-1.99 1.79H4.802a2 2 0 0 1-1.99-1.79L2 7.5ZM7 11a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2H8a1 1 0 0 1-1-1Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef6 = React6.forwardRef(ArchiveBoxIcon);
var ArchiveBoxIcon_default = ForwardRef6;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownCircleIcon.js
var React7 = __toESM(require_react(), 1);
function ArrowDownCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React7.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React7.createElement("title", {
    id: titleId
  }, title) : null, React7.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm.75-11.25a.75.75 0 0 0-1.5 0v4.59L7.3 9.24a.75.75 0 0 0-1.1 1.02l3.25 3.5a.75.75 0 0 0 1.1 0l3.25-3.5a.75.75 0 1 0-1.1-1.02l-1.95 2.1V6.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef7 = React7.forwardRef(ArrowDownCircleIcon);
var ArrowDownCircleIcon_default = ForwardRef7;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownLeftIcon.js
var React8 = __toESM(require_react(), 1);
function ArrowDownLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React8.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React8.createElement("title", {
    id: titleId
  }, title) : null, React8.createElement("path", {
    fillRule: "evenodd",
    d: "M14.78 5.22a.75.75 0 0 0-1.06 0L6.5 12.44V6.75a.75.75 0 0 0-1.5 0v7.5c0 .414.336.75.75.75h7.5a.75.75 0 0 0 0-1.5H7.56l7.22-7.22a.75.75 0 0 0 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef8 = React8.forwardRef(ArrowDownLeftIcon);
var ArrowDownLeftIcon_default = ForwardRef8;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownOnSquareStackIcon.js
var React9 = __toESM(require_react(), 1);
function ArrowDownOnSquareStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React9.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React9.createElement("title", {
    id: titleId
  }, title) : null, React9.createElement("path", {
    fillRule: "evenodd",
    d: "M8 1a.75.75 0 0 1 .75.75V6h-1.5V1.75A.75.75 0 0 1 8 1Zm-.75 5v3.296l-.943-1.048a.75.75 0 1 0-1.114 1.004l2.25 2.5a.75.75 0 0 0 1.114 0l2.25-2.5a.75.75 0 0 0-1.114-1.004L8.75 9.296V6h2A2.25 2.25 0 0 1 13 8.25v4.5A2.25 2.25 0 0 1 10.75 15h-5.5A2.25 2.25 0 0 1 3 12.75v-4.5A2.25 2.25 0 0 1 5.25 6h2ZM7 16.75v-.25h3.75a3.75 3.75 0 0 0 3.75-3.75V10h.25A2.25 2.25 0 0 1 17 12.25v4.5A2.25 2.25 0 0 1 14.75 19h-5.5A2.25 2.25 0 0 1 7 16.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef9 = React9.forwardRef(ArrowDownOnSquareStackIcon);
var ArrowDownOnSquareStackIcon_default = ForwardRef9;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownOnSquareIcon.js
var React10 = __toESM(require_react(), 1);
function ArrowDownOnSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React10.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React10.createElement("title", {
    id: titleId
  }, title) : null, React10.createElement("path", {
    d: "M13.75 7h-3v5.296l1.943-2.048a.75.75 0 0 1 1.114 1.004l-3.25 3.5a.75.75 0 0 1-1.114 0l-3.25-3.5a.75.75 0 1 1 1.114-1.004l1.943 2.048V7h1.5V1.75a.75.75 0 0 0-1.5 0V7h-3A2.25 2.25 0 0 0 4 9.25v7.5A2.25 2.25 0 0 0 6.25 19h7.5A2.25 2.25 0 0 0 16 16.75v-7.5A2.25 2.25 0 0 0 13.75 7Z"
  }));
}
var ForwardRef10 = React10.forwardRef(ArrowDownOnSquareIcon);
var ArrowDownOnSquareIcon_default = ForwardRef10;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownRightIcon.js
var React11 = __toESM(require_react(), 1);
function ArrowDownRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React11.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React11.createElement("title", {
    id: titleId
  }, title) : null, React11.createElement("path", {
    d: "M6.28 5.22a.75.75 0 0 0-1.06 1.06l7.22 7.22H6.75a.75.75 0 0 0 0 1.5h7.5a.747.747 0 0 0 .75-.75v-7.5a.75.75 0 0 0-1.5 0v5.69L6.28 5.22Z"
  }));
}
var ForwardRef11 = React11.forwardRef(ArrowDownRightIcon);
var ArrowDownRightIcon_default = ForwardRef11;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownTrayIcon.js
var React12 = __toESM(require_react(), 1);
function ArrowDownTrayIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React12.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React12.createElement("title", {
    id: titleId
  }, title) : null, React12.createElement("path", {
    d: "M10.75 2.75a.75.75 0 0 0-1.5 0v8.614L6.295 8.235a.75.75 0 1 0-1.09 1.03l4.25 4.5a.75.75 0 0 0 1.09 0l4.25-4.5a.75.75 0 0 0-1.09-1.03l-2.955 3.129V2.75Z"
  }), React12.createElement("path", {
    d: "M3.5 12.75a.75.75 0 0 0-1.5 0v2.5A2.75 2.75 0 0 0 4.75 18h10.5A2.75 2.75 0 0 0 18 15.25v-2.5a.75.75 0 0 0-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5Z"
  }));
}
var ForwardRef12 = React12.forwardRef(ArrowDownTrayIcon);
var ArrowDownTrayIcon_default = ForwardRef12;

// node_modules/@heroicons/react/20/solid/esm/ArrowDownIcon.js
var React13 = __toESM(require_react(), 1);
function ArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React13.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React13.createElement("title", {
    id: titleId
  }, title) : null, React13.createElement("path", {
    fillRule: "evenodd",
    d: "M10 3a.75.75 0 0 1 .75.75v10.638l3.96-4.158a.75.75 0 1 1 1.08 1.04l-5.25 5.5a.75.75 0 0 1-1.08 0l-5.25-5.5a.75.75 0 1 1 1.08-1.04l3.96 4.158V3.75A.75.75 0 0 1 10 3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef13 = React13.forwardRef(ArrowDownIcon);
var ArrowDownIcon_default = ForwardRef13;

// node_modules/@heroicons/react/20/solid/esm/ArrowLeftCircleIcon.js
var React14 = __toESM(require_react(), 1);
function ArrowLeftCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React14.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React14.createElement("title", {
    id: titleId
  }, title) : null, React14.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.25-7.25a.75.75 0 0 0 0-1.5H8.66l2.1-1.95a.75.75 0 1 0-1.02-1.1l-3.5 3.25a.75.75 0 0 0 0 1.1l3.5 3.25a.75.75 0 0 0 1.02-1.1l-2.1-1.95h4.59Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef14 = React14.forwardRef(ArrowLeftCircleIcon);
var ArrowLeftCircleIcon_default = ForwardRef14;

// node_modules/@heroicons/react/20/solid/esm/ArrowLeftEndOnRectangleIcon.js
var React15 = __toESM(require_react(), 1);
function ArrowLeftEndOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React15.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React15.createElement("title", {
    id: titleId
  }, title) : null, React15.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4.25A2.25 2.25 0 0 1 5.25 2h5.5A2.25 2.25 0 0 1 13 4.25v2a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 0 0-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 0 0 .75-.75v-2a.75.75 0 0 1 1.5 0v2A2.25 2.25 0 0 1 10.75 18h-5.5A2.25 2.25 0 0 1 3 15.75V4.25Z",
    clipRule: "evenodd"
  }), React15.createElement("path", {
    fillRule: "evenodd",
    d: "M19 10a.75.75 0 0 0-.75-.75H8.704l1.048-.943a.75.75 0 1 0-1.004-1.114l-2.5 2.25a.75.75 0 0 0 0 1.114l2.5 2.25a.75.75 0 1 0 1.004-1.114l-1.048-.943h9.546A.75.75 0 0 0 19 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef15 = React15.forwardRef(ArrowLeftEndOnRectangleIcon);
var ArrowLeftEndOnRectangleIcon_default = ForwardRef15;

// node_modules/@heroicons/react/20/solid/esm/ArrowLeftOnRectangleIcon.js
var React16 = __toESM(require_react(), 1);
function ArrowLeftOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React16.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React16.createElement("title", {
    id: titleId
  }, title) : null, React16.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4.25A2.25 2.25 0 0 1 5.25 2h5.5A2.25 2.25 0 0 1 13 4.25v2a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 0 0-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 0 0 .75-.75v-2a.75.75 0 0 1 1.5 0v2A2.25 2.25 0 0 1 10.75 18h-5.5A2.25 2.25 0 0 1 3 15.75V4.25Z",
    clipRule: "evenodd"
  }), React16.createElement("path", {
    fillRule: "evenodd",
    d: "M19 10a.75.75 0 0 0-.75-.75H8.704l1.048-.943a.75.75 0 1 0-1.004-1.114l-2.5 2.25a.75.75 0 0 0 0 1.114l2.5 2.25a.75.75 0 1 0 1.004-1.114l-1.048-.943h9.546A.75.75 0 0 0 19 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef16 = React16.forwardRef(ArrowLeftOnRectangleIcon);
var ArrowLeftOnRectangleIcon_default = ForwardRef16;

// node_modules/@heroicons/react/20/solid/esm/ArrowLeftStartOnRectangleIcon.js
var React17 = __toESM(require_react(), 1);
function ArrowLeftStartOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React17.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React17.createElement("title", {
    id: titleId
  }, title) : null, React17.createElement("path", {
    fillRule: "evenodd",
    d: "M17 4.25A2.25 2.25 0 0 0 14.75 2h-5.5A2.25 2.25 0 0 0 7 4.25v2a.75.75 0 0 0 1.5 0v-2a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 .75.75v11.5a.75.75 0 0 1-.75.75h-5.5a.75.75 0 0 1-.75-.75v-2a.75.75 0 0 0-1.5 0v2A2.25 2.25 0 0 0 9.25 18h5.5A2.25 2.25 0 0 0 17 15.75V4.25Z",
    clipRule: "evenodd"
  }), React17.createElement("path", {
    fillRule: "evenodd",
    d: "M14 10a.75.75 0 0 0-.75-.75H3.704l1.048-.943a.75.75 0 1 0-1.004-1.114l-2.5 2.25a.75.75 0 0 0 0 1.114l2.5 2.25a.75.75 0 1 0 1.004-1.114l-1.048-.943h9.546A.75.75 0 0 0 14 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef17 = React17.forwardRef(ArrowLeftStartOnRectangleIcon);
var ArrowLeftStartOnRectangleIcon_default = ForwardRef17;

// node_modules/@heroicons/react/20/solid/esm/ArrowLeftIcon.js
var React18 = __toESM(require_react(), 1);
function ArrowLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React18.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React18.createElement("title", {
    id: titleId
  }, title) : null, React18.createElement("path", {
    fillRule: "evenodd",
    d: "M17 10a.75.75 0 0 1-.75.75H5.612l4.158 3.96a.75.75 0 1 1-1.04 1.08l-5.5-5.25a.75.75 0 0 1 0-1.08l5.5-5.25a.75.75 0 1 1 1.04 1.08L5.612 9.25H16.25A.75.75 0 0 1 17 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef18 = React18.forwardRef(ArrowLeftIcon);
var ArrowLeftIcon_default = ForwardRef18;

// node_modules/@heroicons/react/20/solid/esm/ArrowLongDownIcon.js
var React19 = __toESM(require_react(), 1);
function ArrowLongDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React19.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React19.createElement("title", {
    id: titleId
  }, title) : null, React19.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a.75.75 0 0 1 .75.75v12.59l1.95-2.1a.75.75 0 1 1 1.1 1.02l-3.25 3.5a.75.75 0 0 1-1.1 0l-3.25-3.5a.75.75 0 1 1 1.1-1.02l1.95 2.1V2.75A.75.75 0 0 1 10 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef19 = React19.forwardRef(ArrowLongDownIcon);
var ArrowLongDownIcon_default = ForwardRef19;

// node_modules/@heroicons/react/20/solid/esm/ArrowLongLeftIcon.js
var React20 = __toESM(require_react(), 1);
function ArrowLongLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React20.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React20.createElement("title", {
    id: titleId
  }, title) : null, React20.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a.75.75 0 0 1-.75.75H4.66l2.1 1.95a.75.75 0 1 1-1.02 1.1l-3.5-3.25a.75.75 0 0 1 0-1.1l3.5-3.25a.75.75 0 1 1 1.02 1.1l-2.1 1.95h12.59A.75.75 0 0 1 18 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef20 = React20.forwardRef(ArrowLongLeftIcon);
var ArrowLongLeftIcon_default = ForwardRef20;

// node_modules/@heroicons/react/20/solid/esm/ArrowLongRightIcon.js
var React21 = __toESM(require_react(), 1);
function ArrowLongRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React21.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React21.createElement("title", {
    id: titleId
  }, title) : null, React21.createElement("path", {
    fillRule: "evenodd",
    d: "M2 10a.75.75 0 0 1 .75-.75h12.59l-2.1-1.95a.75.75 0 1 1 1.02-1.1l3.5 3.25a.75.75 0 0 1 0 1.1l-3.5 3.25a.75.75 0 1 1-1.02-1.1l2.1-1.95H2.75A.75.75 0 0 1 2 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef21 = React21.forwardRef(ArrowLongRightIcon);
var ArrowLongRightIcon_default = ForwardRef21;

// node_modules/@heroicons/react/20/solid/esm/ArrowLongUpIcon.js
var React22 = __toESM(require_react(), 1);
function ArrowLongUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React22.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React22.createElement("title", {
    id: titleId
  }, title) : null, React22.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a.75.75 0 0 1-.75-.75V4.66L7.3 6.76a.75.75 0 0 1-1.1-1.02l3.25-3.5a.75.75 0 0 1 1.1 0l3.25 3.5a.75.75 0 1 1-1.1 1.02l-1.95-2.1v12.59A.75.75 0 0 1 10 18Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef22 = React22.forwardRef(ArrowLongUpIcon);
var ArrowLongUpIcon_default = ForwardRef22;

// node_modules/@heroicons/react/20/solid/esm/ArrowPathRoundedSquareIcon.js
var React23 = __toESM(require_react(), 1);
function ArrowPathRoundedSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React23.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React23.createElement("title", {
    id: titleId
  }, title) : null, React23.createElement("path", {
    fillRule: "evenodd",
    d: "M10 4.5c1.215 0 2.417.055 3.604.162a.68.68 0 0 1 .615.597c.124 1.038.208 2.088.25 3.15l-1.689-1.69a.75.75 0 0 0-1.06 1.061l2.999 3a.75.75 0 0 0 1.06 0l3.001-3a.75.75 0 1 0-1.06-1.06l-1.748 1.747a41.31 41.31 0 0 0-.264-3.386 2.18 2.18 0 0 0-1.97-1.913 41.512 41.512 0 0 0-7.477 0 2.18 2.18 0 0 0-1.969 1.913 41.16 41.16 0 0 0-.16 1.61.75.75 0 1 0 1.495.12c.041-.52.093-1.038.154-1.552a.68.68 0 0 1 .615-.597A40.012 40.012 0 0 1 10 4.5ZM5.281 9.22a.75.75 0 0 0-1.06 0l-3.001 3a.75.75 0 1 0 1.06 1.06l1.748-1.747c.042 1.141.13 2.27.264 3.386a2.18 2.18 0 0 0 1.97 1.913 41.533 41.533 0 0 0 7.477 0 2.18 2.18 0 0 0 1.969-1.913c.064-.534.117-1.071.16-1.61a.75.75 0 1 0-1.495-.12c-.041.52-.093 1.037-.154 1.552a.68.68 0 0 1-.615.597 40.013 40.013 0 0 1-7.208 0 .68.68 0 0 1-.615-.597 39.785 39.785 0 0 1-.25-3.15l1.689 1.69a.75.75 0 0 0 1.06-1.061l-2.999-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef23 = React23.forwardRef(ArrowPathRoundedSquareIcon);
var ArrowPathRoundedSquareIcon_default = ForwardRef23;

// node_modules/@heroicons/react/20/solid/esm/ArrowPathIcon.js
var React24 = __toESM(require_react(), 1);
function ArrowPathIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React24.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React24.createElement("title", {
    id: titleId
  }, title) : null, React24.createElement("path", {
    fillRule: "evenodd",
    d: "M15.312 11.424a5.5 5.5 0 0 1-9.201 2.466l-.312-.311h2.433a.75.75 0 0 0 0-1.5H3.989a.75.75 0 0 0-.75.75v4.242a.75.75 0 0 0 1.5 0v-2.43l.31.31a7 7 0 0 0 11.712-*********** 0 0 0-1.449-.39Zm1.23-3.723a.75.75 0 0 0 .219-.53V2.929a.75.75 0 0 0-1.5 0V5.36l-.31-.31A7 7 0 0 0 3.239 8.188a.75.75 0 1 0 1.448.389A5.5 5.5 0 0 1 13.89 6.11l.311.31h-2.432a.75.75 0 0 0 0 1.5h4.243a.75.75 0 0 0 .53-.219Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef24 = React24.forwardRef(ArrowPathIcon);
var ArrowPathIcon_default = ForwardRef24;

// node_modules/@heroicons/react/20/solid/esm/ArrowRightCircleIcon.js
var React25 = __toESM(require_react(), 1);
function ArrowRightCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React25.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React25.createElement("title", {
    id: titleId
  }, title) : null, React25.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM6.75 9.25a.75.75 0 0 0 0 1.5h4.59l-2.1 1.95a.75.75 0 0 0 1.02 1.1l3.5-3.25a.75.75 0 0 0 0-1.1l-3.5-3.25a.75.75 0 1 0-1.02 1.1l2.1 1.95H6.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef25 = React25.forwardRef(ArrowRightCircleIcon);
var ArrowRightCircleIcon_default = ForwardRef25;

// node_modules/@heroicons/react/20/solid/esm/ArrowRightEndOnRectangleIcon.js
var React26 = __toESM(require_react(), 1);
function ArrowRightEndOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React26.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React26.createElement("title", {
    id: titleId
  }, title) : null, React26.createElement("path", {
    fillRule: "evenodd",
    d: "M17 4.25A2.25 2.25 0 0 0 14.75 2h-5.5A2.25 2.25 0 0 0 7 4.25v2a.75.75 0 0 0 1.5 0v-2a.75.75 0 0 1 .75-.75h5.5a.75.75 0 0 1 .75.75v11.5a.75.75 0 0 1-.75.75h-5.5a.75.75 0 0 1-.75-.75v-2a.75.75 0 0 0-1.5 0v2A2.25 2.25 0 0 0 9.25 18h5.5A2.25 2.25 0 0 0 17 15.75V4.25Z",
    clipRule: "evenodd"
  }), React26.createElement("path", {
    fillRule: "evenodd",
    d: "M1 10a.75.75 0 0 1 .75-.75h9.546l-1.048-.943a.75.75 0 1 1 1.004-1.114l2.5 2.25a.75.75 0 0 1 0 1.114l-2.5 2.25a.75.75 0 1 1-1.004-1.114l1.048-.943H1.75A.75.75 0 0 1 1 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef26 = React26.forwardRef(ArrowRightEndOnRectangleIcon);
var ArrowRightEndOnRectangleIcon_default = ForwardRef26;

// node_modules/@heroicons/react/20/solid/esm/ArrowRightOnRectangleIcon.js
var React27 = __toESM(require_react(), 1);
function ArrowRightOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React27.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React27.createElement("title", {
    id: titleId
  }, title) : null, React27.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4.25A2.25 2.25 0 0 1 5.25 2h5.5A2.25 2.25 0 0 1 13 4.25v2a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 0 0-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 0 0 .75-.75v-2a.75.75 0 0 1 1.5 0v2A2.25 2.25 0 0 1 10.75 18h-5.5A2.25 2.25 0 0 1 3 15.75V4.25Z",
    clipRule: "evenodd"
  }), React27.createElement("path", {
    fillRule: "evenodd",
    d: "M6 10a.75.75 0 0 1 .75-.75h9.546l-1.048-.943a.75.75 0 1 1 1.004-1.114l2.5 2.25a.75.75 0 0 1 0 1.114l-2.5 2.25a.75.75 0 1 1-1.004-1.114l1.048-.943H6.75A.75.75 0 0 1 6 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef27 = React27.forwardRef(ArrowRightOnRectangleIcon);
var ArrowRightOnRectangleIcon_default = ForwardRef27;

// node_modules/@heroicons/react/20/solid/esm/ArrowRightStartOnRectangleIcon.js
var React28 = __toESM(require_react(), 1);
function ArrowRightStartOnRectangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React28.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React28.createElement("title", {
    id: titleId
  }, title) : null, React28.createElement("path", {
    fillRule: "evenodd",
    d: "M3 4.25A2.25 2.25 0 0 1 5.25 2h5.5A2.25 2.25 0 0 1 13 4.25v2a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 0-.75-.75h-5.5a.75.75 0 0 0-.75.75v11.5c0 .414.336.75.75.75h5.5a.75.75 0 0 0 .75-.75v-2a.75.75 0 0 1 1.5 0v2A2.25 2.25 0 0 1 10.75 18h-5.5A2.25 2.25 0 0 1 3 15.75V4.25Z",
    clipRule: "evenodd"
  }), React28.createElement("path", {
    fillRule: "evenodd",
    d: "M6 10a.75.75 0 0 1 .75-.75h9.546l-1.048-.943a.75.75 0 1 1 1.004-1.114l2.5 2.25a.75.75 0 0 1 0 1.114l-2.5 2.25a.75.75 0 1 1-1.004-1.114l1.048-.943H6.75A.75.75 0 0 1 6 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef28 = React28.forwardRef(ArrowRightStartOnRectangleIcon);
var ArrowRightStartOnRectangleIcon_default = ForwardRef28;

// node_modules/@heroicons/react/20/solid/esm/ArrowRightIcon.js
var React29 = __toESM(require_react(), 1);
function ArrowRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React29.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React29.createElement("title", {
    id: titleId
  }, title) : null, React29.createElement("path", {
    fillRule: "evenodd",
    d: "M3 10a.75.75 0 0 1 .75-.75h10.638L10.23 5.29a.75.75 0 1 1 1.04-1.08l5.5 5.25a.75.75 0 0 1 0 1.08l-5.5 5.25a.75.75 0 1 1-1.04-1.08l4.158-3.96H3.75A.75.75 0 0 1 3 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef29 = React29.forwardRef(ArrowRightIcon);
var ArrowRightIcon_default = ForwardRef29;

// node_modules/@heroicons/react/20/solid/esm/ArrowSmallDownIcon.js
var React30 = __toESM(require_react(), 1);
function ArrowSmallDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React30.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React30.createElement("title", {
    id: titleId
  }, title) : null, React30.createElement("path", {
    fillRule: "evenodd",
    d: "M10 5a.75.75 0 0 1 .75.75v6.638l1.96-2.158a.75.75 0 1 1 1.08 1.04l-3.25 3.5a.75.75 0 0 1-1.08 0l-3.25-3.5a.75.75 0 1 1 1.08-1.04l1.96 2.158V5.75A.75.75 0 0 1 10 5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef30 = React30.forwardRef(ArrowSmallDownIcon);
var ArrowSmallDownIcon_default = ForwardRef30;

// node_modules/@heroicons/react/20/solid/esm/ArrowSmallLeftIcon.js
var React31 = __toESM(require_react(), 1);
function ArrowSmallLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React31.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React31.createElement("title", {
    id: titleId
  }, title) : null, React31.createElement("path", {
    fillRule: "evenodd",
    d: "M15 10a.75.75 0 0 1-.75.75H7.612l2.158 1.96a.75.75 0 1 1-1.04 1.08l-3.5-3.25a.75.75 0 0 1 0-1.08l3.5-3.25a.75.75 0 1 1 1.04 1.08L7.612 9.25h6.638A.75.75 0 0 1 15 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef31 = React31.forwardRef(ArrowSmallLeftIcon);
var ArrowSmallLeftIcon_default = ForwardRef31;

// node_modules/@heroicons/react/20/solid/esm/ArrowSmallRightIcon.js
var React32 = __toESM(require_react(), 1);
function ArrowSmallRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React32.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React32.createElement("title", {
    id: titleId
  }, title) : null, React32.createElement("path", {
    fillRule: "evenodd",
    d: "M5 10a.75.75 0 0 1 .75-.75h6.638L10.23 7.29a.75.75 0 1 1 1.04-1.08l3.5 3.25a.75.75 0 0 1 0 1.08l-3.5 3.25a.75.75 0 1 1-1.04-1.08l2.158-1.96H5.75A.75.75 0 0 1 5 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef32 = React32.forwardRef(ArrowSmallRightIcon);
var ArrowSmallRightIcon_default = ForwardRef32;

// node_modules/@heroicons/react/20/solid/esm/ArrowSmallUpIcon.js
var React33 = __toESM(require_react(), 1);
function ArrowSmallUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React33.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React33.createElement("title", {
    id: titleId
  }, title) : null, React33.createElement("path", {
    fillRule: "evenodd",
    d: "M10 15a.75.75 0 0 1-.75-.75V7.612L7.29 9.77a.75.75 0 0 1-1.08-1.04l3.25-3.5a.75.75 0 0 1 1.08 0l3.25 3.5a.75.75 0 1 1-1.08 1.04l-1.96-2.158v6.638A.75.75 0 0 1 10 15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef33 = React33.forwardRef(ArrowSmallUpIcon);
var ArrowSmallUpIcon_default = ForwardRef33;

// node_modules/@heroicons/react/20/solid/esm/ArrowTopRightOnSquareIcon.js
var React34 = __toESM(require_react(), 1);
function ArrowTopRightOnSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React34.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React34.createElement("title", {
    id: titleId
  }, title) : null, React34.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 5.5a.75.75 0 0 0-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 0 0 .75-.75v-4a.75.75 0 0 1 1.5 0v4A2.25 2.25 0 0 1 12.75 17h-8.5A2.25 2.25 0 0 1 2 14.75v-8.5A2.25 2.25 0 0 1 4.25 4h5a.75.75 0 0 1 0 1.5h-5Z",
    clipRule: "evenodd"
  }), React34.createElement("path", {
    fillRule: "evenodd",
    d: "M6.194 12.753a.75.75 0 0 0 1.06.053L16.5 4.44v2.81a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.553l-9.056 8.194a.75.75 0 0 0-.053 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef34 = React34.forwardRef(ArrowTopRightOnSquareIcon);
var ArrowTopRightOnSquareIcon_default = ForwardRef34;

// node_modules/@heroicons/react/20/solid/esm/ArrowTrendingDownIcon.js
var React35 = __toESM(require_react(), 1);
function ArrowTrendingDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React35.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React35.createElement("title", {
    id: titleId
  }, title) : null, React35.createElement("path", {
    fillRule: "evenodd",
    d: "M1.22 5.222a.75.75 0 0 1 1.06 0L7 9.942l3.768-3.769a.75.75 0 0 1 1.113.058 20.908 20.908 0 0 1 3.813 7.254l1.574-2.727a.75.75 0 0 1 1.3.75l-2.475 4.286a.75.75 0 0 1-1.025.275l-4.287-2.475a.75.75 0 0 1 .75-1.3l2.71 1.565a19.422 19.422 0 0 0-3.013-6.024L7.53 11.533a.75.75 0 0 1-1.06 0l-5.25-5.25a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef35 = React35.forwardRef(ArrowTrendingDownIcon);
var ArrowTrendingDownIcon_default = ForwardRef35;

// node_modules/@heroicons/react/20/solid/esm/ArrowTrendingUpIcon.js
var React36 = __toESM(require_react(), 1);
function ArrowTrendingUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React36.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React36.createElement("title", {
    id: titleId
  }, title) : null, React36.createElement("path", {
    fillRule: "evenodd",
    d: "M12.577 4.878a.75.75 0 0 1 .919-.53l4.78 1.281a.75.75 0 0 1 .531.919l-1.281 4.78a.75.75 0 0 1-1.449-.387l.81-3.022a19.407 19.407 0 0 0-5.594 *********** 0 0 1-1.139.093L7 10.06l-4.72 4.72a.75.75 0 0 1-1.06-1.061l5.25-5.25a.75.75 0 0 1 1.06 0l3.074 3.073a20.923 20.923 0 0 1 5.545-4.931l-3.042-.815a.75.75 0 0 1-.53-.919Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef36 = React36.forwardRef(ArrowTrendingUpIcon);
var ArrowTrendingUpIcon_default = ForwardRef36;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnDownLeftIcon.js
var React37 = __toESM(require_react(), 1);
function ArrowTurnDownLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React37.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React37.createElement("title", {
    id: titleId
  }, title) : null, React37.createElement("path", {
    fillRule: "evenodd",
    d: "M16.25 3a.75.75 0 0 0-.75.75v7.5H4.56l1.97-1.97a.75.75 0 0 0-1.06-1.06l-3.25 3.25a.75.75 0 0 0 0 1.06l3.25 3.25a.75.75 0 0 0 1.06-1.06l-1.97-1.97h11.69A.75.75 0 0 0 17 12V3.75a.75.75 0 0 0-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef37 = React37.forwardRef(ArrowTurnDownLeftIcon);
var ArrowTurnDownLeftIcon_default = ForwardRef37;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnDownRightIcon.js
var React38 = __toESM(require_react(), 1);
function ArrowTurnDownRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React38.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React38.createElement("title", {
    id: titleId
  }, title) : null, React38.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3a.75.75 0 0 1 .75.75v7.5h10.94l-1.97-1.97a.75.75 0 0 1 1.06-1.06l3.25 3.25a.75.75 0 0 1 0 1.06l-3.25 3.25a.75.75 0 1 1-1.06-1.06l1.97-1.97H3.75A.75.75 0 0 1 3 12V3.75A.75.75 0 0 1 3.75 3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef38 = React38.forwardRef(ArrowTurnDownRightIcon);
var ArrowTurnDownRightIcon_default = ForwardRef38;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnLeftDownIcon.js
var React39 = __toESM(require_react(), 1);
function ArrowTurnLeftDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React39.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React39.createElement("title", {
    id: titleId
  }, title) : null, React39.createElement("path", {
    fillRule: "evenodd",
    d: "M16 3.75a.75.75 0 0 1-.75.75h-7.5v10.94l1.97-1.97a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0l-3.25-3.25a.75.75 0 1 1 1.06-1.06l1.97 1.97V3.75A.75.75 0 0 1 7 3h8.25a.75.75 0 0 1 .75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef39 = React39.forwardRef(ArrowTurnLeftDownIcon);
var ArrowTurnLeftDownIcon_default = ForwardRef39;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnLeftUpIcon.js
var React40 = __toESM(require_react(), 1);
function ArrowTurnLeftUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React40.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React40.createElement("title", {
    id: titleId
  }, title) : null, React40.createElement("path", {
    fillRule: "evenodd",
    d: "M16 16.25a.75.75 0 0 0-.75-.75h-7.5V4.56l1.97 1.97a.75.75 0 1 0 1.06-1.06L7.53 2.22a.75.75 0 0 0-1.06 0L3.22 5.47a.75.75 0 0 0 1.06 1.06l1.97-1.97v11.69c0 .414.336.75.75.75h8.25a.75.75 0 0 0 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef40 = React40.forwardRef(ArrowTurnLeftUpIcon);
var ArrowTurnLeftUpIcon_default = ForwardRef40;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnRightDownIcon.js
var React41 = __toESM(require_react(), 1);
function ArrowTurnRightDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React41.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React41.createElement("title", {
    id: titleId
  }, title) : null, React41.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3.75c0 .414.336.75.75.75h7.5v10.94l-1.97-1.97a.75.75 0 0 0-1.06 1.06l3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06l-1.97 1.97V3.75A.75.75 0 0 0 12 3H3.75a.75.75 0 0 0-.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef41 = React41.forwardRef(ArrowTurnRightDownIcon);
var ArrowTurnRightDownIcon_default = ForwardRef41;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnRightUpIcon.js
var React42 = __toESM(require_react(), 1);
function ArrowTurnRightUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React42.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React42.createElement("title", {
    id: titleId
  }, title) : null, React42.createElement("path", {
    fillRule: "evenodd",
    d: "M3 16.25a.75.75 0 0 1 .75-.75h7.5V4.56L9.28 6.53a.75.75 0 0 1-1.06-1.06l3.25-3.25a.75.75 0 0 1 1.06 0l3.25 3.25a.75.75 0 0 1-1.06 1.06l-1.97-1.97v11.69A.75.75 0 0 1 12 17H3.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef42 = React42.forwardRef(ArrowTurnRightUpIcon);
var ArrowTurnRightUpIcon_default = ForwardRef42;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnUpLeftIcon.js
var React43 = __toESM(require_react(), 1);
function ArrowTurnUpLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React43.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React43.createElement("title", {
    id: titleId
  }, title) : null, React43.createElement("path", {
    fillRule: "evenodd",
    d: "M16.25 17a.75.75 0 0 1-.75-.75v-7.5H4.56l1.97 1.97a.75.75 0 1 1-1.06 1.06L2.22 8.53a.75.75 0 0 1 0-1.06l3.25-3.25a.75.75 0 0 1 1.06 1.06L4.56 7.25h11.69A.75.75 0 0 1 17 8v8.25a.75.75 0 0 1-.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef43 = React43.forwardRef(ArrowTurnUpLeftIcon);
var ArrowTurnUpLeftIcon_default = ForwardRef43;

// node_modules/@heroicons/react/20/solid/esm/ArrowTurnUpRightIcon.js
var React44 = __toESM(require_react(), 1);
function ArrowTurnUpRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React44.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React44.createElement("title", {
    id: titleId
  }, title) : null, React44.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 17a.75.75 0 0 0 .75-.75v-7.5h10.94l-1.97 1.97a.75.75 0 1 0 1.06 1.06l3.25-3.25a.75.75 0 0 0 0-1.06l-3.25-3.25a.75.75 0 1 0-1.06 1.06l1.97 1.97H3.75A.75.75 0 0 0 3 8v8.25c0 .414.336.75.75.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef44 = React44.forwardRef(ArrowTurnUpRightIcon);
var ArrowTurnUpRightIcon_default = ForwardRef44;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpCircleIcon.js
var React45 = __toESM(require_react(), 1);
function ArrowUpCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React45.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React45.createElement("title", {
    id: titleId
  }, title) : null, React45.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm-.75-4.75a.75.75 0 0 0 1.5 0V8.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0L6.2 9.74a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef45 = React45.forwardRef(ArrowUpCircleIcon);
var ArrowUpCircleIcon_default = ForwardRef45;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpLeftIcon.js
var React46 = __toESM(require_react(), 1);
function ArrowUpLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React46.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React46.createElement("title", {
    id: titleId
  }, title) : null, React46.createElement("path", {
    fillRule: "evenodd",
    d: "M14.78 14.78a.75.75 0 0 1-1.06 0L6.5 7.56v5.69a.75.75 0 0 1-1.5 0v-7.5A.75.75 0 0 1 5.75 5h7.5a.75.75 0 0 1 0 1.5H7.56l7.22 7.22a.75.75 0 0 1 0 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef46 = React46.forwardRef(ArrowUpLeftIcon);
var ArrowUpLeftIcon_default = ForwardRef46;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpOnSquareStackIcon.js
var React47 = __toESM(require_react(), 1);
function ArrowUpOnSquareStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React47.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React47.createElement("title", {
    id: titleId
  }, title) : null, React47.createElement("path", {
    fillRule: "evenodd",
    d: "M10.75 6h-2v4.25a.75.75 0 0 1-1.5 0V6h1.5V3.704l.943 1.048a.75.75 0 0 0 1.114-1.004l-2.25-2.5a.75.75 0 0 0-1.114 0l-2.25 2.5a.75.75 0 0 0 1.114 1.004l.943-1.048V6h-2A2.25 2.25 0 0 0 3 8.25v4.5A2.25 2.25 0 0 0 5.25 15h5.5A2.25 2.25 0 0 0 13 12.75v-4.5A2.25 2.25 0 0 0 10.75 6ZM7 16.75v-.25h3.75a3.75 3.75 0 0 0 3.75-3.75V10h.25A2.25 2.25 0 0 1 17 12.25v4.5A2.25 2.25 0 0 1 14.75 19h-5.5A2.25 2.25 0 0 1 7 16.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef47 = React47.forwardRef(ArrowUpOnSquareStackIcon);
var ArrowUpOnSquareStackIcon_default = ForwardRef47;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpOnSquareIcon.js
var React48 = __toESM(require_react(), 1);
function ArrowUpOnSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React48.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React48.createElement("title", {
    id: titleId
  }, title) : null, React48.createElement("path", {
    fillRule: "evenodd",
    d: "M13.75 7h-3V3.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0L6.2 4.74a.75.75 0 0 0 1.1 1.02l1.95-2.1V7h-3A2.25 2.25 0 0 0 4 9.25v7.5A2.25 2.25 0 0 0 6.25 19h7.5A2.25 2.25 0 0 0 16 16.75v-7.5A2.25 2.25 0 0 0 13.75 7Zm-3 0h-1.5v5.25a.75.75 0 0 0 1.5 0V7Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef48 = React48.forwardRef(ArrowUpOnSquareIcon);
var ArrowUpOnSquareIcon_default = ForwardRef48;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpRightIcon.js
var React49 = __toESM(require_react(), 1);
function ArrowUpRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React49.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React49.createElement("title", {
    id: titleId
  }, title) : null, React49.createElement("path", {
    fillRule: "evenodd",
    d: "M5.22 14.78a.75.75 0 0 0 1.06 0l7.22-7.22v5.69a.75.75 0 0 0 1.5 0v-7.5a.75.75 0 0 0-.75-.75h-7.5a.75.75 0 0 0 0 1.5h5.69l-7.22 7.22a.75.75 0 0 0 0 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef49 = React49.forwardRef(ArrowUpRightIcon);
var ArrowUpRightIcon_default = ForwardRef49;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpTrayIcon.js
var React50 = __toESM(require_react(), 1);
function ArrowUpTrayIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React50.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React50.createElement("title", {
    id: titleId
  }, title) : null, React50.createElement("path", {
    d: "M9.25 13.25a.75.75 0 0 0 1.5 0V4.636l2.955 3.129a.75.75 0 0 0 1.09-1.03l-4.25-4.5a.75.75 0 0 0-1.09 0l-4.25 4.5a.75.75 0 1 0 1.09 1.03L9.25 4.636v8.614Z"
  }), React50.createElement("path", {
    d: "M3.5 12.75a.75.75 0 0 0-1.5 0v2.5A2.75 2.75 0 0 0 4.75 18h10.5A2.75 2.75 0 0 0 18 15.25v-2.5a.75.75 0 0 0-1.5 0v2.5c0 .69-.56 1.25-1.25 1.25H4.75c-.69 0-1.25-.56-1.25-1.25v-2.5Z"
  }));
}
var ForwardRef50 = React50.forwardRef(ArrowUpTrayIcon);
var ArrowUpTrayIcon_default = ForwardRef50;

// node_modules/@heroicons/react/20/solid/esm/ArrowUpIcon.js
var React51 = __toESM(require_react(), 1);
function ArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React51.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React51.createElement("title", {
    id: titleId
  }, title) : null, React51.createElement("path", {
    fillRule: "evenodd",
    d: "M10 17a.75.75 0 0 1-.75-.75V5.612L5.29 9.77a.75.75 0 0 1-1.08-1.04l5.25-5.5a.75.75 0 0 1 1.08 0l5.25 5.5a.75.75 0 1 1-1.08 1.04l-3.96-4.158V16.25A.75.75 0 0 1 10 17Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef51 = React51.forwardRef(ArrowUpIcon);
var ArrowUpIcon_default = ForwardRef51;

// node_modules/@heroicons/react/20/solid/esm/ArrowUturnDownIcon.js
var React52 = __toESM(require_react(), 1);
function ArrowUturnDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React52.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React52.createElement("title", {
    id: titleId
  }, title) : null, React52.createElement("path", {
    fillRule: "evenodd",
    d: "M2.232 12.207a.75.75 0 0 1 1.06.025l3.958 4.146V6.375a5.375 5.375 0 0 1 10.75 0V9.25a.75.75 0 0 1-1.5 0V6.375a3.875 3.875 0 0 0-7.75 0v10.003l3.957-4.146a.75.75 0 0 1 1.085 1.036l-5.25 5.5a.75.75 0 0 1-1.085 0l-5.25-5.5a.75.75 0 0 1 .025-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef52 = React52.forwardRef(ArrowUturnDownIcon);
var ArrowUturnDownIcon_default = ForwardRef52;

// node_modules/@heroicons/react/20/solid/esm/ArrowUturnLeftIcon.js
var React53 = __toESM(require_react(), 1);
function ArrowUturnLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React53.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React53.createElement("title", {
    id: titleId
  }, title) : null, React53.createElement("path", {
    fillRule: "evenodd",
    d: "M7.793 2.232a.75.75 0 0 1-.025 1.06L3.622 7.25h10.003a5.375 5.375 0 0 1 0 10.75H10.75a.75.75 0 0 1 0-1.5h2.875a3.875 3.875 0 0 0 0-7.75H3.622l4.146 3.957a.75.75 0 0 1-1.036 1.085l-5.5-5.25a.75.75 0 0 1 0-1.085l5.5-5.25a.75.75 0 0 1 1.06.025Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef53 = React53.forwardRef(ArrowUturnLeftIcon);
var ArrowUturnLeftIcon_default = ForwardRef53;

// node_modules/@heroicons/react/20/solid/esm/ArrowUturnRightIcon.js
var React54 = __toESM(require_react(), 1);
function ArrowUturnRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React54.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React54.createElement("title", {
    id: titleId
  }, title) : null, React54.createElement("path", {
    fillRule: "evenodd",
    d: "M12.207 2.232a.75.75 0 0 0 .025 1.06l4.146 3.958H6.375a5.375 5.375 0 0 0 0 10.75H9.25a.75.75 0 0 0 0-1.5H6.375a3.875 3.875 0 0 1 0-7.75h10.003l-4.146 3.957a.75.75 0 0 0 1.036 1.085l5.5-5.25a.75.75 0 0 0 0-1.085l-5.5-5.25a.75.75 0 0 0-1.06.025Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef54 = React54.forwardRef(ArrowUturnRightIcon);
var ArrowUturnRightIcon_default = ForwardRef54;

// node_modules/@heroicons/react/20/solid/esm/ArrowUturnUpIcon.js
var React55 = __toESM(require_react(), 1);
function ArrowUturnUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React55.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React55.createElement("title", {
    id: titleId
  }, title) : null, React55.createElement("path", {
    fillRule: "evenodd",
    d: "M17.768 7.793a.75.75 0 0 1-1.06-.025L12.75 3.622v10.003a5.375 5.375 0 0 1-10.75 0V10.75a.75.75 0 0 1 1.5 0v2.875a3.875 3.875 0 0 0 7.75 0V3.622L7.293 7.768a.75.75 0 0 1-1.086-1.036l5.25-5.5a.75.75 0 0 1 1.085 0l5.25 5.5a.75.75 0 0 1-.024 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef55 = React55.forwardRef(ArrowUturnUpIcon);
var ArrowUturnUpIcon_default = ForwardRef55;

// node_modules/@heroicons/react/20/solid/esm/ArrowsPointingInIcon.js
var React56 = __toESM(require_react(), 1);
function ArrowsPointingInIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React56.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React56.createElement("title", {
    id: titleId
  }, title) : null, React56.createElement("path", {
    d: "M3.28 2.22a.75.75 0 0 0-1.06 1.06L5.44 6.5H2.75a.75.75 0 0 0 0 1.5h4.5A.75.75 0 0 0 8 7.25v-4.5a.75.75 0 0 0-1.5 0v2.69L3.28 2.22ZM13.5 2.75a.75.75 0 0 0-1.5 0v4.5c0 .414.336.75.75.75h4.5a.75.75 0 0 0 0-1.5h-2.69l3.22-3.22a.75.75 0 0 0-1.06-1.06L13.5 5.44V2.75ZM3.28 17.78l3.22-3.22v2.69a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.69l-3.22 3.22a.75.75 0 1 0 1.06 1.06ZM13.5 14.56l3.22 3.22a.75.75 0 1 0 1.06-1.06l-3.22-3.22h2.69a.75.75 0 0 0 0-1.5h-4.5a.75.75 0 0 0-.75.75v4.5a.75.75 0 0 0 1.5 0v-2.69Z"
  }));
}
var ForwardRef56 = React56.forwardRef(ArrowsPointingInIcon);
var ArrowsPointingInIcon_default = ForwardRef56;

// node_modules/@heroicons/react/20/solid/esm/ArrowsPointingOutIcon.js
var React57 = __toESM(require_react(), 1);
function ArrowsPointingOutIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React57.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React57.createElement("title", {
    id: titleId
  }, title) : null, React57.createElement("path", {
    d: "m13.28 7.78 3.22-3.22v2.69a.75.75 0 0 0 1.5 0v-4.5a.75.75 0 0 0-.75-.75h-4.5a.75.75 0 0 0 0 1.5h2.69l-3.22 3.22a.75.75 0 0 0 1.06 1.06ZM2 17.25v-4.5a.75.75 0 0 1 1.5 0v2.69l3.22-3.22a.75.75 0 0 1 1.06 1.06L4.56 16.5h2.69a.75.75 0 0 1 0 1.5h-4.5a.747.747 0 0 1-.75-.75ZM12.22 13.28l3.22 3.22h-2.69a.75.75 0 0 0 0 1.5h4.5a.747.747 0 0 0 .75-.75v-4.5a.75.75 0 0 0-1.5 0v2.69l-3.22-3.22a.75.75 0 1 0-1.06 1.06ZM3.5 4.56l3.22 3.22a.75.75 0 0 0 1.06-1.06L4.56 3.5h2.69a.75.75 0 0 0 0-1.5h-4.5a.75.75 0 0 0-.75.75v4.5a.75.75 0 0 0 1.5 0V4.56Z"
  }));
}
var ForwardRef57 = React57.forwardRef(ArrowsPointingOutIcon);
var ArrowsPointingOutIcon_default = ForwardRef57;

// node_modules/@heroicons/react/20/solid/esm/ArrowsRightLeftIcon.js
var React58 = __toESM(require_react(), 1);
function ArrowsRightLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React58.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React58.createElement("title", {
    id: titleId
  }, title) : null, React58.createElement("path", {
    fillRule: "evenodd",
    d: "M13.2 2.24a.75.75 0 0 0 .04 1.06l2.1 1.95H6.75a.75.75 0 0 0 0 1.5h8.59l-2.1 1.95a.75.75 0 1 0 1.02 1.1l3.5-3.25a.75.75 0 0 0 0-1.1l-3.5-3.25a.75.75 0 0 0-1.06.04Zm-6.4 8a.75.75 0 0 0-1.06-.04l-3.5 3.25a.75.75 0 0 0 0 1.1l3.5 3.25a.75.75 0 1 0 1.02-1.1l-2.1-1.95h8.59a.75.75 0 0 0 0-1.5H4.66l2.1-1.95a.75.75 0 0 0 .04-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef58 = React58.forwardRef(ArrowsRightLeftIcon);
var ArrowsRightLeftIcon_default = ForwardRef58;

// node_modules/@heroicons/react/20/solid/esm/ArrowsUpDownIcon.js
var React59 = __toESM(require_react(), 1);
function ArrowsUpDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React59.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React59.createElement("title", {
    id: titleId
  }, title) : null, React59.createElement("path", {
    fillRule: "evenodd",
    d: "M2.24 6.8a.75.75 0 0 0 1.06-.04l1.95-2.1v8.59a.75.75 0 0 0 1.5 0V4.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0L2.2 5.74a.75.75 0 0 0 .04 1.06Zm8 6.4a.75.75 0 0 0-.04 1.06l3.25 3.5a.75.75 0 0 0 1.1 0l3.25-3.5a.75.75 0 1 0-1.1-1.02l-1.95 2.1V6.75a.75.75 0 0 0-1.5 0v8.59l-1.95-2.1a.75.75 0 0 0-1.06-.04Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef59 = React59.forwardRef(ArrowsUpDownIcon);
var ArrowsUpDownIcon_default = ForwardRef59;

// node_modules/@heroicons/react/20/solid/esm/AtSymbolIcon.js
var React60 = __toESM(require_react(), 1);
function AtSymbolIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React60.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React60.createElement("title", {
    id: titleId
  }, title) : null, React60.createElement("path", {
    fillRule: "evenodd",
    d: "M5.404 14.596A6.5 6.5 0 1 1 16.5 10a1.25 1.25 0 0 1-2.5 0 4 4 0 1 0-.571 2.06A2.75 2.75 0 0 0 18 10a8 8 0 1 0-2.343 5.657.75.75 0 0 0-1.06-1.06 6.5 6.5 0 0 1-9.193 0ZM10 7.5a2.5 2.5 0 1 0 0 5 2.5 2.5 0 0 0 0-5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef60 = React60.forwardRef(AtSymbolIcon);
var AtSymbolIcon_default = ForwardRef60;

// node_modules/@heroicons/react/20/solid/esm/BackspaceIcon.js
var React61 = __toESM(require_react(), 1);
function BackspaceIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React61.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React61.createElement("title", {
    id: titleId
  }, title) : null, React61.createElement("path", {
    fillRule: "evenodd",
    d: "M7.22 3.22A.75.75 0 0 1 7.75 3h9A2.25 2.25 0 0 1 19 5.25v9.5A2.25 2.25 0 0 1 16.75 17h-9a.75.75 0 0 1-.53-.22L.97 10.53a.75.75 0 0 1 0-1.06l6.25-6.25Zm3.06 4a.75.75 0 1 0-1.06 1.06L10.94 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 11.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L12 8.94l-1.72-1.72Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef61 = React61.forwardRef(BackspaceIcon);
var BackspaceIcon_default = ForwardRef61;

// node_modules/@heroicons/react/20/solid/esm/BackwardIcon.js
var React62 = __toESM(require_react(), 1);
function BackwardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React62.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React62.createElement("title", {
    id: titleId
  }, title) : null, React62.createElement("path", {
    d: "M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"
  }));
}
var ForwardRef62 = React62.forwardRef(BackwardIcon);
var BackwardIcon_default = ForwardRef62;

// node_modules/@heroicons/react/20/solid/esm/BanknotesIcon.js
var React63 = __toESM(require_react(), 1);
function BanknotesIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React63.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React63.createElement("title", {
    id: titleId
  }, title) : null, React63.createElement("path", {
    fillRule: "evenodd",
    d: "M1 4a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v8a1 1 0 0 1-1 1H2a1 1 0 0 1-1-1V4Zm12 4a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM4 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm13-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM1.75 14.5a.75.75 0 0 0 0 1.5c4.417 0 8.693.603 12.749 1.73 1.111.309 2.251-.512 2.251-1.696v-.784a.75.75 0 0 0-1.5 0v.784a.272.272 0 0 1-.35.25A49.043 49.043 0 0 0 1.75 14.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef63 = React63.forwardRef(BanknotesIcon);
var BanknotesIcon_default = ForwardRef63;

// node_modules/@heroicons/react/20/solid/esm/Bars2Icon.js
var React64 = __toESM(require_react(), 1);
function Bars2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React64.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React64.createElement("title", {
    id: titleId
  }, title) : null, React64.createElement("path", {
    fillRule: "evenodd",
    d: "M2 6.75A.75.75 0 0 1 2.75 6h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 6.75Zm0 6.5a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef64 = React64.forwardRef(Bars2Icon);
var Bars2Icon_default = ForwardRef64;

// node_modules/@heroicons/react/20/solid/esm/Bars3BottomLeftIcon.js
var React65 = __toESM(require_react(), 1);
function Bars3BottomLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React65.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React65.createElement("title", {
    id: titleId
  }, title) : null, React65.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75Zm0 10.5a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1-.75-.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef65 = React65.forwardRef(Bars3BottomLeftIcon);
var Bars3BottomLeftIcon_default = ForwardRef65;

// node_modules/@heroicons/react/20/solid/esm/Bars3BottomRightIcon.js
var React66 = __toESM(require_react(), 1);
function Bars3BottomRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React66.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React66.createElement("title", {
    id: titleId
  }, title) : null, React66.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75Zm7 10.5a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1-.75-.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef66 = React66.forwardRef(Bars3BottomRightIcon);
var Bars3BottomRightIcon_default = ForwardRef66;

// node_modules/@heroicons/react/20/solid/esm/Bars3CenterLeftIcon.js
var React67 = __toESM(require_react(), 1);
function Bars3CenterLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React67.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React67.createElement("title", {
    id: titleId
  }, title) : null, React67.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75Zm0 10.5a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75ZM2 10a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-7.5A.75.75 0 0 1 2 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef67 = React67.forwardRef(Bars3CenterLeftIcon);
var Bars3CenterLeftIcon_default = ForwardRef67;

// node_modules/@heroicons/react/20/solid/esm/Bars3Icon.js
var React68 = __toESM(require_react(), 1);
function Bars3Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React68.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React68.createElement("title", {
    id: titleId
  }, title) : null, React68.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.75A.75.75 0 0 1 2.75 4h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75ZM2 10a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 10Zm0 5.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef68 = React68.forwardRef(Bars3Icon);
var Bars3Icon_default = ForwardRef68;

// node_modules/@heroicons/react/20/solid/esm/Bars4Icon.js
var React69 = __toESM(require_react(), 1);
function Bars4Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React69.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React69.createElement("title", {
    id: titleId
  }, title) : null, React69.createElement("path", {
    fillRule: "evenodd",
    d: "M2 3.75A.75.75 0 0 1 2.75 3h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75Zm0 4.167a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Zm0 4.166a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Zm0 4.167a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef69 = React69.forwardRef(Bars4Icon);
var Bars4Icon_default = ForwardRef69;

// node_modules/@heroicons/react/20/solid/esm/BarsArrowDownIcon.js
var React70 = __toESM(require_react(), 1);
function BarsArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React70.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React70.createElement("title", {
    id: titleId
  }, title) : null, React70.createElement("path", {
    fillRule: "evenodd",
    d: "M2 3.75A.75.75 0 0 1 2.75 3h11.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 7.5a.75.75 0 0 1 .75-.75h7.508a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 7.5ZM14 7a.75.75 0 0 1 .75.75v6.59l1.95-2.1a.75.75 0 1 1 1.1 1.02l-3.25 3.5a.75.75 0 0 1-1.1 0l-3.25-3.5a.75.75 0 1 1 1.1-1.02l1.95 2.1V7.75A.75.75 0 0 1 14 7ZM2 11.25a.75.75 0 0 1 .75-.75h4.562a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef70 = React70.forwardRef(BarsArrowDownIcon);
var BarsArrowDownIcon_default = ForwardRef70;

// node_modules/@heroicons/react/20/solid/esm/BarsArrowUpIcon.js
var React71 = __toESM(require_react(), 1);
function BarsArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React71.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React71.createElement("title", {
    id: titleId
  }, title) : null, React71.createElement("path", {
    fillRule: "evenodd",
    d: "M2 3.75A.75.75 0 0 1 2.75 3h11.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 3.75ZM2 7.5a.75.75 0 0 1 .75-.75h6.365a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 7.5ZM14 7a.75.75 0 0 1 .55.24l3.25 3.5a.75.75 0 1 1-1.1 1.02l-1.95-2.1v6.59a.75.75 0 0 1-1.5 0V9.66l-1.95 2.1a.75.75 0 1 1-1.1-1.02l3.25-3.5A.75.75 0 0 1 14 7ZM2 11.25a.75.75 0 0 1 .75-.75H7A.75.75 0 0 1 7 12H2.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef71 = React71.forwardRef(BarsArrowUpIcon);
var BarsArrowUpIcon_default = ForwardRef71;

// node_modules/@heroicons/react/20/solid/esm/Battery0Icon.js
var React72 = __toESM(require_react(), 1);
function Battery0Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React72.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React72.createElement("title", {
    id: titleId
  }, title) : null, React72.createElement("path", {
    fillRule: "evenodd",
    d: "M1 7.25A2.25 2.25 0 0 1 3.25 5h12.5A2.25 2.25 0 0 1 18 7.25v1.085a1.5 1.5 0 0 1 1 1.415v.5a1.5 1.5 0 0 1-1 1.415v1.085A2.25 2.25 0 0 1 15.75 15H3.25A2.25 2.25 0 0 1 1 12.75v-5.5Zm2.25-.75a.75.75 0 0 0-.75.75v5.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-5.5a.75.75 0 0 0-.75-.75H3.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef72 = React72.forwardRef(Battery0Icon);
var Battery0Icon_default = ForwardRef72;

// node_modules/@heroicons/react/20/solid/esm/Battery100Icon.js
var React73 = __toESM(require_react(), 1);
function Battery100Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React73.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React73.createElement("title", {
    id: titleId
  }, title) : null, React73.createElement("path", {
    d: "M4.75 8a.75.75 0 0 0-.75.75v2.5c0 .414.336.75.75.75h9.5a.75.75 0 0 0 .75-.75v-2.5a.75.75 0 0 0-.75-.75h-9.5Z"
  }), React73.createElement("path", {
    fillRule: "evenodd",
    d: "M1 7.25A2.25 2.25 0 0 1 3.25 5h12.5A2.25 2.25 0 0 1 18 7.25v1.085a1.5 1.5 0 0 1 1 1.415v.5a1.5 1.5 0 0 1-1 1.415v1.085A2.25 2.25 0 0 1 15.75 15H3.25A2.25 2.25 0 0 1 1 12.75v-5.5Zm2.25-.75a.75.75 0 0 0-.75.75v5.5c0 .414.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-5.5a.75.75 0 0 0-.75-.75H3.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef73 = React73.forwardRef(Battery100Icon);
var Battery100Icon_default = ForwardRef73;

// node_modules/@heroicons/react/20/solid/esm/Battery50Icon.js
var React74 = __toESM(require_react(), 1);
function Battery50Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React74.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React74.createElement("title", {
    id: titleId
  }, title) : null, React74.createElement("path", {
    d: "M4.75 8a.75.75 0 0 0-.75.75v2.5c0 .414.336.75.75.75H9.5a.75.75 0 0 0 .75-.75v-2.5A.75.75 0 0 0 9.5 8H4.75Z"
  }), React74.createElement("path", {
    fillRule: "evenodd",
    d: "M3.25 5A2.25 2.25 0 0 0 1 7.25v5.5A2.25 2.25 0 0 0 3.25 15h12.5A2.25 2.25 0 0 0 18 12.75v-1.085a1.5 1.5 0 0 0 1-1.415v-.5a1.5 1.5 0 0 0-1-1.415V7.25A2.25 2.25 0 0 0 15.75 5H3.25ZM2.5 7.25a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-.75.75H3.25a.75.75 0 0 1-.75-.75v-5.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef74 = React74.forwardRef(Battery50Icon);
var Battery50Icon_default = ForwardRef74;

// node_modules/@heroicons/react/20/solid/esm/BeakerIcon.js
var React75 = __toESM(require_react(), 1);
function BeakerIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React75.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React75.createElement("title", {
    id: titleId
  }, title) : null, React75.createElement("path", {
    fillRule: "evenodd",
    d: "M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef75 = React75.forwardRef(BeakerIcon);
var BeakerIcon_default = ForwardRef75;

// node_modules/@heroicons/react/20/solid/esm/BellAlertIcon.js
var React76 = __toESM(require_react(), 1);
function BellAlertIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React76.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React76.createElement("title", {
    id: titleId
  }, title) : null, React76.createElement("path", {
    d: "M4.214 3.227a.75.75 0 0 0-1.156-.955 8.97 8.97 0 0 0-1.856 3.825.75.75 0 0 0 1.466.316 7.47 7.47 0 0 1 1.546-3.186ZM16.942 2.272a.75.75 0 0 0-1.157.955 7.47 7.47 0 0 1 1.547 *********** 0 0 0 1.466-.316 8.971 8.971 0 0 0-1.856-3.825Z"
  }), React76.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a6 6 0 0 0-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 0 0 .515 1.076 32.91 32.91 0 0 0 3.256.508 3.5 3.5 0 0 0 6.972 0 32.903 32.903 0 0 0 3.256-.508.75.75 0 0 0 .515-1.076A11.448 11.448 0 0 1 16 8a6 6 0 0 0-6-6Zm0 14.5a2 2 0 0 1-1.95-1.557 33.54 33.54 0 0 0 3.9 0A2 2 0 0 1 10 16.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef76 = React76.forwardRef(BellAlertIcon);
var BellAlertIcon_default = ForwardRef76;

// node_modules/@heroicons/react/20/solid/esm/BellSlashIcon.js
var React77 = __toESM(require_react(), 1);
function BellSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React77.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React77.createElement("title", {
    id: titleId
  }, title) : null, React77.createElement("path", {
    d: "M4 8c0-.26.017-.517.049-.77l7.722 7.723a33.56 33.56 0 0 1-3.722-.01 2 2 0 0 0 3.862.15l1.134 1.134a3.5 3.5 0 0 1-6.53-1.409 32.91 32.91 0 0 1-3.257-.508.75.75 0 0 1-.515-1.076A11.448 11.448 0 0 0 4 8ZM17.266 13.9a.756.756 0 0 1-.068.116L6.389 3.207A6 6 0 0 1 16 8c.001 1.887.455 3.665 1.258 5.234a.75.75 0 0 1 .01.666ZM3.28 2.22a.75.75 0 0 0-1.06 1.06l14.5 14.5a.75.75 0 1 0 1.06-1.06L3.28 2.22Z"
  }));
}
var ForwardRef77 = React77.forwardRef(BellSlashIcon);
var BellSlashIcon_default = ForwardRef77;

// node_modules/@heroicons/react/20/solid/esm/BellSnoozeIcon.js
var React78 = __toESM(require_react(), 1);
function BellSnoozeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React78.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React78.createElement("title", {
    id: titleId
  }, title) : null, React78.createElement("path", {
    fillRule: "evenodd",
    d: "M4 8a6 6 0 1 1 12 0c0 1.887.454 3.665 1.257 5.234a.75.75 0 0 1-.515 1.076 32.903 32.903 0 0 1-3.256.508 3.5 3.5 0 0 1-6.972 0 32.91 32.91 0 0 1-3.256-.508.75.75 0 0 1-.515-1.076A11.448 11.448 0 0 0 4 8Zm6 7c-.655 0-1.305-.02-1.95-.057a2 2 0 0 0 3.9 0c-.645.038-1.295.057-1.95.057ZM8.75 6a.75.75 0 0 0 0 1.5h1.043L8.14 9.814A.75.75 0 0 0 8.75 11h2.5a.75.75 0 0 0 0-1.5h-1.043l1.653-2.314A.75.75 0 0 0 11.25 6h-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef78 = React78.forwardRef(BellSnoozeIcon);
var BellSnoozeIcon_default = ForwardRef78;

// node_modules/@heroicons/react/20/solid/esm/BellIcon.js
var React79 = __toESM(require_react(), 1);
function BellIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React79.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React79.createElement("title", {
    id: titleId
  }, title) : null, React79.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a6 6 0 0 0-6 6c0 1.887-.454 3.665-1.257 5.234a.75.75 0 0 0 .515 1.076 32.91 32.91 0 0 0 3.256.508 3.5 3.5 0 0 0 6.972 0 32.903 32.903 0 0 0 3.256-.508.75.75 0 0 0 .515-1.076A11.448 11.448 0 0 1 16 8a6 6 0 0 0-6-6ZM8.05 14.943a33.54 33.54 0 0 0 3.9 0 2 2 0 0 1-3.9 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef79 = React79.forwardRef(BellIcon);
var BellIcon_default = ForwardRef79;

// node_modules/@heroicons/react/20/solid/esm/BoldIcon.js
var React80 = __toESM(require_react(), 1);
function BoldIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React80.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React80.createElement("title", {
    id: titleId
  }, title) : null, React80.createElement("path", {
    fillRule: "evenodd",
    d: "M4 3a1 1 0 0 1 1-1h6a4.5 4.5 0 0 1 3.274 7.587A4.75 4.75 0 0 1 11.25 18H5a1 1 0 0 1-1-1V3Zm2.5 5.5v-4H11a2 2 0 1 1 0 4H6.5Zm0 2.5v4.5h4.75a2.25 2.25 0 0 0 0-4.5H6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef80 = React80.forwardRef(BoldIcon);
var BoldIcon_default = ForwardRef80;

// node_modules/@heroicons/react/20/solid/esm/BoltSlashIcon.js
var React81 = __toESM(require_react(), 1);
function BoltSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React81.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React81.createElement("title", {
    id: titleId
  }, title) : null, React81.createElement("path", {
    fillRule: "evenodd",
    d: "M2.22 2.22a.75.75 0 0 1 1.06 0l14.5 14.5a.75.75 0 1 1-1.06 1.06L2.22 3.28a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }), React81.createElement("path", {
    d: "M4.73 7.912 2.191 10.75A.75.75 0 0 0 2.75 12h6.068L4.73 7.912ZM9.233 12.415l-1.216 5.678a.75.75 0 0 0 1.292.657l2.956-3.303-3.032-3.032ZM15.27 12.088l2.539-2.838A.75.75 0 0 0 17.25 8h-6.068l4.088 4.088ZM10.767 7.585l1.216-5.678a.75.75 0 0 0-1.292-.657L7.735 4.553l3.032 3.032Z"
  }));
}
var ForwardRef81 = React81.forwardRef(BoltSlashIcon);
var BoltSlashIcon_default = ForwardRef81;

// node_modules/@heroicons/react/20/solid/esm/BoltIcon.js
var React82 = __toESM(require_react(), 1);
function BoltIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React82.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React82.createElement("title", {
    id: titleId
  }, title) : null, React82.createElement("path", {
    d: "M11.983 1.907a.75.75 0 0 0-1.292-.657l-8.5 9.5A.75.75 0 0 0 2.75 12h6.572l-1.305 6.093a.75.75 0 0 0 1.292.657l8.5-9.5A.75.75 0 0 0 17.25 8h-6.572l1.305-6.093Z"
  }));
}
var ForwardRef82 = React82.forwardRef(BoltIcon);
var BoltIcon_default = ForwardRef82;

// node_modules/@heroicons/react/20/solid/esm/BookOpenIcon.js
var React83 = __toESM(require_react(), 1);
function BookOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React83.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React83.createElement("title", {
    id: titleId
  }, title) : null, React83.createElement("path", {
    d: "M10.75 16.82A7.462 7.462 0 0 1 15 15.5c.71 0 1.396.098 2.046.282A.75.75 0 0 0 18 15.06v-11a.75.75 0 0 0-.546-.721A9.006 9.006 0 0 0 15 3a8.963 8.963 0 0 0-4.25 1.065V16.82ZM9.25 4.065A8.963 8.963 0 0 0 5 3c-.85 0-1.673.118-2.454.339A.75.75 0 0 0 2 4.06v11a.75.75 0 0 0 .954.721A7.506 7.506 0 0 1 5 15.5c1.579 0 3.042.487 4.25 1.32V4.065Z"
  }));
}
var ForwardRef83 = React83.forwardRef(BookOpenIcon);
var BookOpenIcon_default = ForwardRef83;

// node_modules/@heroicons/react/20/solid/esm/BookmarkSlashIcon.js
var React84 = __toESM(require_react(), 1);
function BookmarkSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React84.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React84.createElement("title", {
    id: titleId
  }, title) : null, React84.createElement("path", {
    d: "M17 4.517v9.301L5.433 2.252a41.44 41.44 0 0 1 9.637.058C16.194 2.45 17 3.414 17 4.517ZM3 17.25V6.182l10.654 10.654L10 15.082l-5.925 2.844A.75.75 0 0 1 3 17.25ZM3.28 2.22a.75.75 0 0 0-1.06 1.06l14.5 14.5a.75.75 0 1 0 1.06-1.06L3.28 2.22Z"
  }));
}
var ForwardRef84 = React84.forwardRef(BookmarkSlashIcon);
var BookmarkSlashIcon_default = ForwardRef84;

// node_modules/@heroicons/react/20/solid/esm/BookmarkSquareIcon.js
var React85 = __toESM(require_react(), 1);
function BookmarkSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React85.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React85.createElement("title", {
    id: titleId
  }, title) : null, React85.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 2A2.25 2.25 0 0 0 2 4.25v11.5A2.25 2.25 0 0 0 4.25 18h11.5A2.25 2.25 0 0 0 18 15.75V4.25A2.25 2.25 0 0 0 15.75 2H4.25ZM6 13.25V3.5h8v9.75a.75.75 0 0 1-1.064.681L10 12.576l-2.936 1.355A.75.75 0 0 1 6 13.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef85 = React85.forwardRef(BookmarkSquareIcon);
var BookmarkSquareIcon_default = ForwardRef85;

// node_modules/@heroicons/react/20/solid/esm/BookmarkIcon.js
var React86 = __toESM(require_react(), 1);
function BookmarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React86.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React86.createElement("title", {
    id: titleId
  }, title) : null, React86.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2c-1.716 0-3.408.106-5.07.31C3.806 2.45 3 3.414 3 4.517V17.25a.75.75 0 0 0 1.075.676L10 15.082l5.925 2.844A.75.75 0 0 0 17 17.25V4.517c0-1.103-.806-2.068-1.93-2.207A41.403 41.403 0 0 0 10 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef86 = React86.forwardRef(BookmarkIcon);
var BookmarkIcon_default = ForwardRef86;

// node_modules/@heroicons/react/20/solid/esm/BriefcaseIcon.js
var React87 = __toESM(require_react(), 1);
function BriefcaseIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React87.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React87.createElement("title", {
    id: titleId
  }, title) : null, React87.createElement("path", {
    fillRule: "evenodd",
    d: "M6 3.75A2.75 2.75 0 0 1 8.75 1h2.5A2.75 2.75 0 0 1 14 3.75v.443c.572.055 1.14.122 1.706.2C17.053 4.582 18 5.75 18 7.07v3.469c0 1.126-.694 2.191-1.83 2.54-1.952.599-4.024.921-6.17.921s-4.219-.322-6.17-.921C2.694 12.73 2 11.665 2 10.539V7.07c0-1.321.947-2.489 2.294-2.676A41.047 41.047 0 0 1 6 4.193V3.75Zm6.5 0v.325a41.622 41.622 0 0 0-5 0V3.75c0-.69.56-1.25 1.25-1.25h2.5c.69 0 1.25.56 1.25 1.25ZM10 10a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1h.01a1 1 0 0 0 1-1V11a1 1 0 0 0-1-1H10Z",
    clipRule: "evenodd"
  }), React87.createElement("path", {
    d: "M3 15.055v-.684c.*************.39.142 2.092.642 4.313.987 6.61.987 2.297 0 4.518-.345 6.61-.987.135-.041.264-.089.39-.142v.684c0 1.347-.985 2.53-2.363 2.686a41.454 41.454 0 0 1-9.274 0C3.985 17.585 3 16.402 3 15.055Z"
  }));
}
var ForwardRef87 = React87.forwardRef(BriefcaseIcon);
var BriefcaseIcon_default = ForwardRef87;

// node_modules/@heroicons/react/20/solid/esm/BugAntIcon.js
var React88 = __toESM(require_react(), 1);
function BugAntIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React88.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React88.createElement("title", {
    id: titleId
  }, title) : null, React88.createElement("path", {
    fillRule: "evenodd",
    d: "M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 *********** 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 *********** 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-*********** 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-*********** 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-*********** 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 ***********-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-*********** 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef88 = React88.forwardRef(BugAntIcon);
var BugAntIcon_default = ForwardRef88;

// node_modules/@heroicons/react/20/solid/esm/BuildingLibraryIcon.js
var React89 = __toESM(require_react(), 1);
function BuildingLibraryIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React89.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React89.createElement("title", {
    id: titleId
  }, title) : null, React89.createElement("path", {
    fillRule: "evenodd",
    d: "M9.674 2.075a.75.75 0 0 1 .652 0l7.25 3.5A.75.75 0 0 1 17 6.957V16.5h.25a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5H3V6.957a.75.75 0 0 1-.576-1.382l7.25-3.5ZM11 6a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM7.5 9.75a.75.75 0 0 0-1.5 0v5.5a.75.75 0 0 0 1.5 0v-5.5Zm3.25 0a.75.75 0 0 0-1.5 0v5.5a.75.75 0 0 0 1.5 0v-5.5Zm3.25 0a.75.75 0 0 0-1.5 0v5.5a.75.75 0 0 0 1.5 0v-5.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef89 = React89.forwardRef(BuildingLibraryIcon);
var BuildingLibraryIcon_default = ForwardRef89;

// node_modules/@heroicons/react/20/solid/esm/BuildingOffice2Icon.js
var React90 = __toESM(require_react(), 1);
function BuildingOffice2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React90.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React90.createElement("title", {
    id: titleId
  }, title) : null, React90.createElement("path", {
    fillRule: "evenodd",
    d: "M1 2.75A.75.75 0 0 1 1.75 2h10.5a.75.75 0 0 1 0 1.5H12v13.75a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1-.75-.75v-2.5a.75.75 0 0 0-.75-.75h-2.5a.75.75 0 0 0-.75.75v2.5a.75.75 0 0 1-.75.75h-2.5a.75.75 0 0 1 0-1.5H2v-13h-.25A.75.75 0 0 1 1 2.75ZM4 5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM4.5 9a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1ZM8 5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM8.5 9a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1ZM14.25 6a.75.75 0 0 0-.75.75V17a1 1 0 0 0 1 1h3.75a.75.75 0 0 0 0-1.5H18v-9h.25a.75.75 0 0 0 0-1.5h-4Zm.5 3.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm.5 3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef90 = React90.forwardRef(BuildingOffice2Icon);
var BuildingOffice2Icon_default = ForwardRef90;

// node_modules/@heroicons/react/20/solid/esm/BuildingOfficeIcon.js
var React91 = __toESM(require_react(), 1);
function BuildingOfficeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React91.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React91.createElement("title", {
    id: titleId
  }, title) : null, React91.createElement("path", {
    fillRule: "evenodd",
    d: "M4 16.5v-13h-.25a.75.75 0 0 1 0-1.5h12.5a.75.75 0 0 1 0 1.5H16v13h.25a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1-.75-.75v-2.5a.75.75 0 0 0-.75-.75h-2.5a.75.75 0 0 0-.75.75v2.5a.75.75 0 0 1-.75.75h-3.5a.75.75 0 0 1 0-1.5H4Zm3-11a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1ZM7.5 9a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1ZM11 5.5a.5.5 0 0 1 .5-.5h1a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5h-1a.5.5 0 0 1-.5-.5v-1Zm.5 3.5a.5.5 0 0 0-.5.5v1a.5.5 0 0 0 .5.5h1a.5.5 0 0 0 .5-.5v-1a.5.5 0 0 0-.5-.5h-1Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef91 = React91.forwardRef(BuildingOfficeIcon);
var BuildingOfficeIcon_default = ForwardRef91;

// node_modules/@heroicons/react/20/solid/esm/BuildingStorefrontIcon.js
var React92 = __toESM(require_react(), 1);
function BuildingStorefrontIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React92.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React92.createElement("title", {
    id: titleId
  }, title) : null, React92.createElement("path", {
    d: "M2.879 7.121A3 3 0 0 0 7.5 6.66a2.997 2.997 0 0 0 2.5 1.34 2.997 2.997 0 0 0 2.5-1.34 3 3 0 1 0 4.622-3.78l-.293-.293A2 2 0 0 0 15.415 2H4.585a2 2 0 0 0-1.414.586l-.292.292a3 3 0 0 0 0 4.243ZM3 9.032a4.507 4.507 0 0 0 4.5-.29A4.48 4.48 0 0 0 10 9.5a4.48 4.48 0 0 0 2.5-.758 4.507 4.507 0 0 0 4.5.29V16.5h.25a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 0-.75-.75h-2.5a.75.75 0 0 0-.75.75v3.5a.75.75 0 0 1-.75.75h-4.5a.75.75 0 0 1 0-1.5H3V9.032Z"
  }));
}
var ForwardRef92 = React92.forwardRef(BuildingStorefrontIcon);
var BuildingStorefrontIcon_default = ForwardRef92;

// node_modules/@heroicons/react/20/solid/esm/CakeIcon.js
var React93 = __toESM(require_react(), 1);
function CakeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React93.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React93.createElement("title", {
    id: titleId
  }, title) : null, React93.createElement("path", {
    d: "m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"
  }));
}
var ForwardRef93 = React93.forwardRef(CakeIcon);
var CakeIcon_default = ForwardRef93;

// node_modules/@heroicons/react/20/solid/esm/CalculatorIcon.js
var React94 = __toESM(require_react(), 1);
function CalculatorIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React94.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React94.createElement("title", {
    id: titleId
  }, title) : null, React94.createElement("path", {
    fillRule: "evenodd",
    d: "M10 1c-1.716 0-3.408.106-5.07.31C3.806 1.45 3 2.414 3 3.517V16.75A2.25 2.25 0 0 0 5.25 19h9.5A2.25 2.25 0 0 0 17 16.75V3.517c0-1.103-.806-2.068-1.93-2.207A41.403 41.403 0 0 0 10 1ZM5.99 8.75A.75.75 0 0 1 6.74 8h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01Zm-.75 2.916a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01Zm1.417-5.75a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01Zm-.75 2.916a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01Zm1.42-5.75a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01Zm-.75 2.916a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01ZM12.5 8.75a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm.75 1.417a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75v-.01a.75.75 0 0 0-.75-.75h-.01Zm0 2.166a.75.75 0 0 1 .75.75v2.167a.75.75 0 1 1-1.5 0v-2.167a.75.75 0 0 1 .75-.75ZM6.75 4a.75.75 0 0 0-.75.75v.5c0 .414.336.75.75.75h6.5a.75.75 0 0 0 .75-.75v-.5a.75.75 0 0 0-.75-.75h-6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef94 = React94.forwardRef(CalculatorIcon);
var CalculatorIcon_default = ForwardRef94;

// node_modules/@heroicons/react/20/solid/esm/CalendarDateRangeIcon.js
var React95 = __toESM(require_react(), 1);
function CalendarDateRangeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React95.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React95.createElement("title", {
    id: titleId
  }, title) : null, React95.createElement("path", {
    d: "M10 9.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V10a.75.75 0 0 0-.75-.75H10ZM6 13.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V14a.75.75 0 0 0-.75-.75H6ZM8 13.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V14a.75.75 0 0 0-.75-.75H8ZM9.25 14a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H10a.75.75 0 0 1-.75-.75V14ZM12 11.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V12a.75.75 0 0 0-.75-.75H12ZM12 13.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V14a.75.75 0 0 0-.75-.75H12ZM13.25 12a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H14a.75.75 0 0 1-.75-.75V12ZM11.25 10.005c0-.417.338-.755.755-.755h2a.755.755 0 1 1 0 1.51h-2a.755.755 0 0 1-.755-.755ZM6.005 11.25a.755.755 0 1 0 0 1.51h4a.755.755 0 1 0 0-1.51h-4Z"
  }), React95.createElement("path", {
    fillRule: "evenodd",
    d: "M5.75 2a.75.75 0 0 1 .75.75V4h7V2.75a.75.75 0 0 1 1.5 0V4h.25A2.75 2.75 0 0 1 18 6.75v8.5A2.75 2.75 0 0 1 15.25 18H4.75A2.75 2.75 0 0 1 2 15.25v-8.5A2.75 2.75 0 0 1 4.75 4H5V2.75A.75.75 0 0 1 5.75 2Zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef95 = React95.forwardRef(CalendarDateRangeIcon);
var CalendarDateRangeIcon_default = ForwardRef95;

// node_modules/@heroicons/react/20/solid/esm/CalendarDaysIcon.js
var React96 = __toESM(require_react(), 1);
function CalendarDaysIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React96.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React96.createElement("title", {
    id: titleId
  }, title) : null, React96.createElement("path", {
    d: "M5.25 12a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H6a.75.75 0 0 1-.75-.75V12ZM6 13.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V14a.75.75 0 0 0-.75-.75H6ZM7.25 12a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H8a.75.75 0 0 1-.75-.75V12ZM8 13.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V14a.75.75 0 0 0-.75-.75H8ZM9.25 10a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H10a.75.75 0 0 1-.75-.75V10ZM10 11.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V12a.75.75 0 0 0-.75-.75H10ZM9.25 14a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H10a.75.75 0 0 1-.75-.75V14ZM12 9.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V10a.75.75 0 0 0-.75-.75H12ZM11.25 12a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H12a.75.75 0 0 1-.75-.75V12ZM12 13.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V14a.75.75 0 0 0-.75-.75H12ZM13.25 10a.75.75 0 0 1 .75-.75h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H14a.75.75 0 0 1-.75-.75V10ZM14 11.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V12a.75.75 0 0 0-.75-.75H14Z"
  }), React96.createElement("path", {
    fillRule: "evenodd",
    d: "M5.75 2a.75.75 0 0 1 .75.75V4h7V2.75a.75.75 0 0 1 1.5 0V4h.25A2.75 2.75 0 0 1 18 6.75v8.5A2.75 2.75 0 0 1 15.25 18H4.75A2.75 2.75 0 0 1 2 15.25v-8.5A2.75 2.75 0 0 1 4.75 4H5V2.75A.75.75 0 0 1 5.75 2Zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef96 = React96.forwardRef(CalendarDaysIcon);
var CalendarDaysIcon_default = ForwardRef96;

// node_modules/@heroicons/react/20/solid/esm/CalendarIcon.js
var React97 = __toESM(require_react(), 1);
function CalendarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React97.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React97.createElement("title", {
    id: titleId
  }, title) : null, React97.createElement("path", {
    fillRule: "evenodd",
    d: "M5.75 2a.75.75 0 0 1 .75.75V4h7V2.75a.75.75 0 0 1 1.5 0V4h.25A2.75 2.75 0 0 1 18 6.75v8.5A2.75 2.75 0 0 1 15.25 18H4.75A2.75 2.75 0 0 1 2 15.25v-8.5A2.75 2.75 0 0 1 4.75 4H5V2.75A.75.75 0 0 1 5.75 2Zm-1 5.5c-.69 0-1.25.56-1.25 1.25v6.5c0 .69.56 1.25 1.25 1.25h10.5c.69 0 1.25-.56 1.25-1.25v-6.5c0-.69-.56-1.25-1.25-1.25H4.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef97 = React97.forwardRef(CalendarIcon);
var CalendarIcon_default = ForwardRef97;

// node_modules/@heroicons/react/20/solid/esm/CameraIcon.js
var React98 = __toESM(require_react(), 1);
function CameraIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React98.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React98.createElement("title", {
    id: titleId
  }, title) : null, React98.createElement("path", {
    fillRule: "evenodd",
    d: "M1 8a2 2 0 0 1 2-2h.93a2 2 0 0 0 1.664-.89l.812-1.22A2 2 0 0 1 8.07 3h3.86a2 2 0 0 1 1.664.89l.812 1.22A2 2 0 0 0 16.07 6H17a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8Zm13.5 3a4.5 4.5 0 1 1-9 0 4.5 4.5 0 0 1 9 0ZM10 14a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef98 = React98.forwardRef(CameraIcon);
var CameraIcon_default = ForwardRef98;

// node_modules/@heroicons/react/20/solid/esm/ChartBarSquareIcon.js
var React99 = __toESM(require_react(), 1);
function ChartBarSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React99.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React99.createElement("title", {
    id: titleId
  }, title) : null, React99.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 2A2.25 2.25 0 0 0 2 4.25v11.5A2.25 2.25 0 0 0 4.25 18h11.5A2.25 2.25 0 0 0 18 15.75V4.25A2.25 2.25 0 0 0 15.75 2H4.25ZM15 5.75a.75.75 0 0 0-1.5 0v8.5a.75.75 0 0 0 1.5 0v-8.5Zm-8.5 6a.75.75 0 0 0-1.5 0v2.5a.75.75 0 0 0 1.5 0v-2.5ZM8.584 9a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5a.75.75 0 0 1 .75-.75Zm3.58-1.25a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef99 = React99.forwardRef(ChartBarSquareIcon);
var ChartBarSquareIcon_default = ForwardRef99;

// node_modules/@heroicons/react/20/solid/esm/ChartBarIcon.js
var React100 = __toESM(require_react(), 1);
function ChartBarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React100.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React100.createElement("title", {
    id: titleId
  }, title) : null, React100.createElement("path", {
    d: "M15.5 2A1.5 1.5 0 0 0 14 3.5v13a1.5 1.5 0 0 0 1.5 1.5h1a1.5 1.5 0 0 0 1.5-1.5v-13A1.5 1.5 0 0 0 16.5 2h-1ZM9.5 6A1.5 1.5 0 0 0 8 7.5v9A1.5 1.5 0 0 0 9.5 18h1a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 10.5 6h-1ZM3.5 10A1.5 1.5 0 0 0 2 11.5v5A1.5 1.5 0 0 0 3.5 18h1A1.5 1.5 0 0 0 6 16.5v-5A1.5 1.5 0 0 0 4.5 10h-1Z"
  }));
}
var ForwardRef100 = React100.forwardRef(ChartBarIcon);
var ChartBarIcon_default = ForwardRef100;

// node_modules/@heroicons/react/20/solid/esm/ChartPieIcon.js
var React101 = __toESM(require_react(), 1);
function ChartPieIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React101.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React101.createElement("title", {
    id: titleId
  }, title) : null, React101.createElement("path", {
    d: "M12 9a1 1 0 0 1-1-1V3c0-.552.45-1.007.997-.93a7.004 7.004 0 0 1 5.933 5.933c.078.547-.378.997-.93.997h-5Z"
  }), React101.createElement("path", {
    d: "M8.003 4.07C8.55 3.994 9 4.449 9 5v5a1 1 0 0 0 1 1h5c.552 0 1.008.45.93.997A7.001 7.001 0 0 1 2 11a7.002 7.002 0 0 1 6.003-6.93Z"
  }));
}
var ForwardRef101 = React101.forwardRef(ChartPieIcon);
var ChartPieIcon_default = ForwardRef101;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleBottomCenterTextIcon.js
var React102 = __toESM(require_react(), 1);
function ChatBubbleBottomCenterTextIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React102.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React102.createElement("title", {
    id: titleId
  }, title) : null, React102.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2c-2.236 0-4.43.18-6.57.524C1.993 2.755 1 4.014 1 5.426v5.148c0 1.413.993 2.67 2.43 2.902 1.168.188 2.352.327 3.55.414.28.02.521.18.642.413l1.713 3.293a.75.75 0 0 0 1.33 0l1.713-3.293a.783.783 0 0 1 .642-.413 41.102 41.102 0 0 0 3.55-.414c1.437-.231 2.43-1.49 2.43-2.902V5.426c0-1.413-.993-2.67-2.43-2.902A41.289 41.289 0 0 0 10 2ZM6.75 6a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 2.5a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5h-3.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef102 = React102.forwardRef(ChatBubbleBottomCenterTextIcon);
var ChatBubbleBottomCenterTextIcon_default = ForwardRef102;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleBottomCenterIcon.js
var React103 = __toESM(require_react(), 1);
function ChatBubbleBottomCenterIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React103.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React103.createElement("title", {
    id: titleId
  }, title) : null, React103.createElement("path", {
    fillRule: "evenodd",
    d: "M3.43 2.524A41.29 41.29 0 0 1 10 2c2.236 0 4.43.18 6.57.524 1.437.231 2.43 1.49 2.43 2.902v5.148c0 1.413-.993 2.67-2.43 2.902a41.102 41.102 0 0 1-3.55.414c-.28.02-.521.18-.643.413l-1.712 3.293a.75.75 0 0 1-1.33 0l-1.713-3.293a.783.783 0 0 0-.642-.413 41.108 41.108 0 0 1-3.55-.414C1.993 13.245 1 11.986 1 10.574V5.426c0-1.413.993-2.67 2.43-2.902Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef103 = React103.forwardRef(ChatBubbleBottomCenterIcon);
var ChatBubbleBottomCenterIcon_default = ForwardRef103;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleLeftEllipsisIcon.js
var React104 = __toESM(require_react(), 1);
function ChatBubbleLeftEllipsisIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React104.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React104.createElement("title", {
    id: titleId
  }, title) : null, React104.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2c-2.236 0-4.43.18-6.57.524C1.993 2.755 1 4.014 1 5.426v5.148c0 1.413.993 2.67 2.43 2.902.848.137 1.705.248 2.57.331v3.443a.75.75 0 0 0 1.28.53l3.58-3.579a.78.78 0 0 1 .527-.224 41.202 41.202 0 0 0 5.183-.5c1.437-.232 2.43-1.49 2.43-2.903V5.426c0-1.413-.993-2.67-2.43-2.902A41.289 41.289 0 0 0 10 2Zm0 7a1 1 0 1 0 0-2 1 1 0 0 0 0 2ZM8 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm5 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef104 = React104.forwardRef(ChatBubbleLeftEllipsisIcon);
var ChatBubbleLeftEllipsisIcon_default = ForwardRef104;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleLeftRightIcon.js
var React105 = __toESM(require_react(), 1);
function ChatBubbleLeftRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React105.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React105.createElement("title", {
    id: titleId
  }, title) : null, React105.createElement("path", {
    d: "M3.505 2.365A41.369 41.369 0 0 1 9 2c1.863 0 3.697.124 5.495.365 1.247.167 2.18 1.108 2.435 2.268a4.45 4.45 0 0 0-.577-.069 43.141 43.141 0 0 0-4.706 0C9.229 4.696 7.5 6.727 7.5 8.998v2.24c0 1.413.67 2.735 1.76 3.562l-2.98 2.98A.75.75 0 0 1 5 17.25v-3.443c-.501-.048-1-.106-1.495-.172C2.033 13.438 1 12.162 1 10.72V5.28c0-1.441 1.033-2.717 2.505-2.914Z"
  }), React105.createElement("path", {
    d: "M14 6c-.762 0-1.52.02-2.271.062C10.157 6.148 9 7.472 9 8.998v2.24c0 1.519 1.147 2.839 2.71 2.935.214.013.428.024.642.034.2.009.385.09.518.224l2.35 2.35a.75.75 0 0 0 1.28-.531v-2.07c1.453-.195 2.5-1.463 2.5-2.915V8.998c0-1.526-1.157-2.85-2.729-2.936A41.645 41.645 0 0 0 14 6Z"
  }));
}
var ForwardRef105 = React105.forwardRef(ChatBubbleLeftRightIcon);
var ChatBubbleLeftRightIcon_default = ForwardRef105;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleLeftIcon.js
var React106 = __toESM(require_react(), 1);
function ChatBubbleLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React106.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React106.createElement("title", {
    id: titleId
  }, title) : null, React106.createElement("path", {
    fillRule: "evenodd",
    d: "M3.43 2.524A41.29 41.29 0 0 1 10 2c2.236 0 4.43.18 6.57.524 1.437.231 2.43 1.49 2.43 2.902v5.148c0 1.413-.993 2.67-2.43 2.902a41.202 41.202 0 0 1-5.183.501.78.78 0 0 0-.528.224l-3.579 3.58A.75.75 0 0 1 6 17.25v-3.443a41.033 41.033 0 0 1-2.57-.33C1.993 13.244 1 11.986 1 10.573V5.426c0-1.413.993-2.67 2.43-2.902Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef106 = React106.forwardRef(ChatBubbleLeftIcon);
var ChatBubbleLeftIcon_default = ForwardRef106;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleOvalLeftEllipsisIcon.js
var React107 = __toESM(require_react(), 1);
function ChatBubbleOvalLeftEllipsisIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React107.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React107.createElement("title", {
    id: titleId
  }, title) : null, React107.createElement("path", {
    fillRule: "evenodd",
    d: "M10 3c-4.31 0-8 3.033-8 7 0 2.024.978 3.825 2.499 5.085a3.478 3.478 0 0 1-.522 1.756.75.75 0 0 0 .584 1.143 5.976 5.976 0 0 0 3.936-1.108c.487.082.99.124 1.503.124 4.31 0 8-3.033 8-7s-3.69-7-8-7Zm0 8a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm-2-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm5 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef107 = React107.forwardRef(ChatBubbleOvalLeftEllipsisIcon);
var ChatBubbleOvalLeftEllipsisIcon_default = ForwardRef107;

// node_modules/@heroicons/react/20/solid/esm/ChatBubbleOvalLeftIcon.js
var React108 = __toESM(require_react(), 1);
function ChatBubbleOvalLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React108.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React108.createElement("title", {
    id: titleId
  }, title) : null, React108.createElement("path", {
    fillRule: "evenodd",
    d: "M2 10c0-3.967 3.69-7 8-7 4.31 0 8 3.033 8 7s-3.69 7-8 7a9.165 9.165 0 0 1-1.504-.123 5.976 5.976 0 0 1-3.935 *********** 0 0 1-.584-1.143 3.478 3.478 0 0 0 .522-1.756C2.979 13.825 2 12.025 2 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef108 = React108.forwardRef(ChatBubbleOvalLeftIcon);
var ChatBubbleOvalLeftIcon_default = ForwardRef108;

// node_modules/@heroicons/react/20/solid/esm/CheckBadgeIcon.js
var React109 = __toESM(require_react(), 1);
function CheckBadgeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React109.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React109.createElement("title", {
    id: titleId
  }, title) : null, React109.createElement("path", {
    fillRule: "evenodd",
    d: "M16.403 12.652a3 3 0 0 0 0-5.304 3 3 0 0 0-3.75-3.751 3 3 0 0 0-5.305 0 3 3 0 0 0-3.751 3.75 3 3 0 0 0 0 5.305 3 3 0 0 0 3.75 3.751 3 3 0 0 0 5.305 0 3 3 0 0 0 3.751-3.75Zm-2.546-4.46a.75.75 0 0 0-1.214-.883l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef109 = React109.forwardRef(CheckBadgeIcon);
var CheckBadgeIcon_default = ForwardRef109;

// node_modules/@heroicons/react/20/solid/esm/CheckCircleIcon.js
var React110 = __toESM(require_react(), 1);
function CheckCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React110.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React110.createElement("title", {
    id: titleId
  }, title) : null, React110.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.857-9.809a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef110 = React110.forwardRef(CheckCircleIcon);
var CheckCircleIcon_default = ForwardRef110;

// node_modules/@heroicons/react/20/solid/esm/CheckIcon.js
var React111 = __toESM(require_react(), 1);
function CheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React111.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React111.createElement("title", {
    id: titleId
  }, title) : null, React111.createElement("path", {
    fillRule: "evenodd",
    d: "M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef111 = React111.forwardRef(CheckIcon);
var CheckIcon_default = ForwardRef111;

// node_modules/@heroicons/react/20/solid/esm/ChevronDoubleDownIcon.js
var React112 = __toESM(require_react(), 1);
function ChevronDoubleDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React112.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React112.createElement("title", {
    id: titleId
  }, title) : null, React112.createElement("path", {
    fillRule: "evenodd",
    d: "M9.47 15.28a.75.75 0 0 0 1.06 0l4.25-4.25a.75.75 0 1 0-1.06-1.06L10 13.69 6.28 9.97a.75.75 0 0 0-1.06 1.06l4.25 4.25ZM5.22 6.03l4.25 4.25a.75.75 0 0 0 1.06 0l4.25-4.25a.75.75 0 0 0-1.06-1.06L10 8.69 6.28 4.97a.75.75 0 0 0-1.06 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef112 = React112.forwardRef(ChevronDoubleDownIcon);
var ChevronDoubleDownIcon_default = ForwardRef112;

// node_modules/@heroicons/react/20/solid/esm/ChevronDoubleLeftIcon.js
var React113 = __toESM(require_react(), 1);
function ChevronDoubleLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React113.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React113.createElement("title", {
    id: titleId
  }, title) : null, React113.createElement("path", {
    fillRule: "evenodd",
    d: "M4.72 9.47a.75.75 0 0 0 0 1.06l4.25 4.25a.75.75 0 1 0 1.06-1.06L6.31 10l3.72-3.72a.75.75 0 1 0-1.06-1.06L4.72 9.47Zm9.25-4.25L9.72 9.47a.75.75 0 0 0 0 1.06l4.25 4.25a.75.75 0 1 0 1.06-1.06L11.31 10l3.72-3.72a.75.75 0 0 0-1.06-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef113 = React113.forwardRef(ChevronDoubleLeftIcon);
var ChevronDoubleLeftIcon_default = ForwardRef113;

// node_modules/@heroicons/react/20/solid/esm/ChevronDoubleRightIcon.js
var React114 = __toESM(require_react(), 1);
function ChevronDoubleRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React114.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React114.createElement("title", {
    id: titleId
  }, title) : null, React114.createElement("path", {
    fillRule: "evenodd",
    d: "M15.28 9.47a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 1 1-1.06-1.06L13.69 10 9.97 6.28a.75.75 0 0 1 1.06-1.06l4.25 4.25ZM6.03 5.22l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L8.69 10 4.97 6.28a.75.75 0 0 1 1.06-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef114 = React114.forwardRef(ChevronDoubleRightIcon);
var ChevronDoubleRightIcon_default = ForwardRef114;

// node_modules/@heroicons/react/20/solid/esm/ChevronDoubleUpIcon.js
var React115 = __toESM(require_react(), 1);
function ChevronDoubleUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React115.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React115.createElement("title", {
    id: titleId
  }, title) : null, React115.createElement("path", {
    fillRule: "evenodd",
    d: "M9.47 4.72a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 6.31l-3.72 3.72a.75.75 0 1 1-1.06-1.06l4.25-4.25Zm-4.25 9.25 4.25-4.25a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 11.31l-3.72 3.72a.75.75 0 0 1-1.06-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef115 = React115.forwardRef(ChevronDoubleUpIcon);
var ChevronDoubleUpIcon_default = ForwardRef115;

// node_modules/@heroicons/react/20/solid/esm/ChevronDownIcon.js
var React116 = __toESM(require_react(), 1);
function ChevronDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React116.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React116.createElement("title", {
    id: titleId
  }, title) : null, React116.createElement("path", {
    fillRule: "evenodd",
    d: "M5.22 8.22a.75.75 0 0 1 1.06 0L10 11.94l3.72-3.72a.75.75 0 1 1 1.06 1.06l-4.25 4.25a.75.75 0 0 1-1.06 0L5.22 9.28a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef116 = React116.forwardRef(ChevronDownIcon);
var ChevronDownIcon_default = ForwardRef116;

// node_modules/@heroicons/react/20/solid/esm/ChevronLeftIcon.js
var React117 = __toESM(require_react(), 1);
function ChevronLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React117.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React117.createElement("title", {
    id: titleId
  }, title) : null, React117.createElement("path", {
    fillRule: "evenodd",
    d: "M11.78 5.22a.75.75 0 0 1 0 1.06L8.06 10l3.72 3.72a.75.75 0 1 1-1.06 1.06l-4.25-4.25a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef117 = React117.forwardRef(ChevronLeftIcon);
var ChevronLeftIcon_default = ForwardRef117;

// node_modules/@heroicons/react/20/solid/esm/ChevronRightIcon.js
var React118 = __toESM(require_react(), 1);
function ChevronRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React118.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React118.createElement("title", {
    id: titleId
  }, title) : null, React118.createElement("path", {
    fillRule: "evenodd",
    d: "M8.22 5.22a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L11.94 10 8.22 6.28a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef118 = React118.forwardRef(ChevronRightIcon);
var ChevronRightIcon_default = ForwardRef118;

// node_modules/@heroicons/react/20/solid/esm/ChevronUpDownIcon.js
var React119 = __toESM(require_react(), 1);
function ChevronUpDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React119.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React119.createElement("title", {
    id: titleId
  }, title) : null, React119.createElement("path", {
    fillRule: "evenodd",
    d: "M10.53 3.47a.75.75 0 0 0-1.06 0L6.22 6.72a.75.75 0 0 0 1.06 1.06L10 5.06l2.72 2.72a.75.75 0 1 0 1.06-1.06l-3.25-3.25Zm-4.31 9.81 3.25 3.25a.75.75 0 0 0 1.06 0l3.25-3.25a.75.75 0 1 0-1.06-1.06L10 14.94l-2.72-2.72a.75.75 0 0 0-1.06 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef119 = React119.forwardRef(ChevronUpDownIcon);
var ChevronUpDownIcon_default = ForwardRef119;

// node_modules/@heroicons/react/20/solid/esm/ChevronUpIcon.js
var React120 = __toESM(require_react(), 1);
function ChevronUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React120.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React120.createElement("title", {
    id: titleId
  }, title) : null, React120.createElement("path", {
    fillRule: "evenodd",
    d: "M9.47 6.47a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 1 1-1.06 1.06L10 8.06l-3.72 3.72a.75.75 0 0 1-1.06-1.06l4.25-4.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef120 = React120.forwardRef(ChevronUpIcon);
var ChevronUpIcon_default = ForwardRef120;

// node_modules/@heroicons/react/20/solid/esm/CircleStackIcon.js
var React121 = __toESM(require_react(), 1);
function CircleStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React121.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React121.createElement("title", {
    id: titleId
  }, title) : null, React121.createElement("path", {
    fillRule: "evenodd",
    d: "M10 1c3.866 0 7 1.79 7 4s-3.134 4-7 4-7-1.79-7-4 3.134-4 7-4Zm5.694 8.13c.464-.264.91-.583 1.306-.952V10c0 2.21-3.134 4-7 4s-7-1.79-7-4V8.178c.396.37.842.688 1.306.953C5.838 10.006 7.854 10.5 10 10.5s4.162-.494 5.694-1.37ZM3 13.179V15c0 2.21 3.134 4 7 4s7-1.79 7-4v-1.822c-.396.37-.842.688-1.306.953-1.532.875-3.548 1.369-5.694 1.369s-4.162-.494-5.694-1.37A7.009 7.009 0 0 1 3 13.179Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef121 = React121.forwardRef(CircleStackIcon);
var CircleStackIcon_default = ForwardRef121;

// node_modules/@heroicons/react/20/solid/esm/ClipboardDocumentCheckIcon.js
var React122 = __toESM(require_react(), 1);
function ClipboardDocumentCheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React122.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React122.createElement("title", {
    id: titleId
  }, title) : null, React122.createElement("path", {
    fillRule: "evenodd",
    d: "M18 5.25a2.25 2.25 0 0 0-2.012-2.238A2.25 2.25 0 0 0 13.75 1h-1.5a2.25 2.25 0 0 0-2.238 2.012c-.875.092-1.6.686-1.884 1.488H11A2.5 2.5 0 0 1 13.5 7v7h2.25A2.25 2.25 0 0 0 18 11.75v-6.5ZM12.25 2.5a.75.75 0 0 0-.75.75v.25h3v-.25a.75.75 0 0 0-.75-.75h-1.5Z",
    clipRule: "evenodd"
  }), React122.createElement("path", {
    fillRule: "evenodd",
    d: "M3 6a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V7a1 1 0 0 0-1-1H3Zm6.874 4.166a.75.75 0 1 0-1.248-.832l-2.493 3.739-.853-.853a.75.75 0 0 0-1.06 1.06l1.5 1.5a.75.75 0 0 0 1.154-.114l3-4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef122 = React122.forwardRef(ClipboardDocumentCheckIcon);
var ClipboardDocumentCheckIcon_default = ForwardRef122;

// node_modules/@heroicons/react/20/solid/esm/ClipboardDocumentListIcon.js
var React123 = __toESM(require_react(), 1);
function ClipboardDocumentListIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React123.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React123.createElement("title", {
    id: titleId
  }, title) : null, React123.createElement("path", {
    fillRule: "evenodd",
    d: "M15.988 3.012A2.25 2.25 0 0 1 18 5.25v6.5A2.25 2.25 0 0 1 15.75 14H13.5V7A2.5 2.5 0 0 0 11 4.5H8.128a2.252 2.252 0 0 1 1.884-1.488A2.25 2.25 0 0 1 12.25 1h1.5a2.25 2.25 0 0 1 2.238 2.012ZM11.5 3.25a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 .75.75v.25h-3v-.25Z",
    clipRule: "evenodd"
  }), React123.createElement("path", {
    fillRule: "evenodd",
    d: "M2 7a1 1 0 0 1 1-1h8a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V7Zm2 3.25a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Zm0 3.5a.75.75 0 0 1 .75-.75h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef123 = React123.forwardRef(ClipboardDocumentListIcon);
var ClipboardDocumentListIcon_default = ForwardRef123;

// node_modules/@heroicons/react/20/solid/esm/ClipboardDocumentIcon.js
var React124 = __toESM(require_react(), 1);
function ClipboardDocumentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React124.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React124.createElement("title", {
    id: titleId
  }, title) : null, React124.createElement("path", {
    fillRule: "evenodd",
    d: "M15.988 3.012A2.25 2.25 0 0 1 18 5.25v6.5A2.25 2.25 0 0 1 15.75 14H13.5v-3.379a3 3 0 0 0-.879-2.121l-3.12-3.121a3 3 0 0 0-1.402-.791 2.252 2.252 0 0 1 1.913-1.576A2.25 2.25 0 0 1 12.25 1h1.5a2.25 2.25 0 0 1 2.238 2.012ZM11.5 3.25a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 .75.75v.25h-3v-.25Z",
    clipRule: "evenodd"
  }), React124.createElement("path", {
    d: "M3.5 6A1.5 1.5 0 0 0 2 7.5v9A1.5 1.5 0 0 0 3.5 18h7a1.5 1.5 0 0 0 1.5-1.5v-5.879a1.5 1.5 0 0 0-.44-1.06L8.44 6.439A1.5 1.5 0 0 0 7.378 6H3.5Z"
  }));
}
var ForwardRef124 = React124.forwardRef(ClipboardDocumentIcon);
var ClipboardDocumentIcon_default = ForwardRef124;

// node_modules/@heroicons/react/20/solid/esm/ClipboardIcon.js
var React125 = __toESM(require_react(), 1);
function ClipboardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React125.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React125.createElement("title", {
    id: titleId
  }, title) : null, React125.createElement("path", {
    fillRule: "evenodd",
    d: "M13.887 3.182c.396.037.79.08 1.183.128C16.194 3.45 17 4.414 17 5.517V16.75A2.25 2.25 0 0 1 14.75 19h-9.5A2.25 2.25 0 0 1 3 16.75V5.517c0-1.103.806-2.068 1.93-2.207.393-.048.787-.09 1.183-.128A3.001 3.001 0 0 1 9 1h2c1.373 0 2.531.923 2.887 2.182ZM7.5 4A1.5 1.5 0 0 1 9 2.5h2A1.5 1.5 0 0 1 12.5 4v.5h-5V4Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef125 = React125.forwardRef(ClipboardIcon);
var ClipboardIcon_default = ForwardRef125;

// node_modules/@heroicons/react/20/solid/esm/ClockIcon.js
var React126 = __toESM(require_react(), 1);
function ClockIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React126.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React126.createElement("title", {
    id: titleId
  }, title) : null, React126.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm.75-13a.75.75 0 0 0-1.5 0v5c0 .414.336.75.75.75h4a.75.75 0 0 0 0-1.5h-3.25V5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef126 = React126.forwardRef(ClockIcon);
var ClockIcon_default = ForwardRef126;

// node_modules/@heroicons/react/20/solid/esm/CloudArrowDownIcon.js
var React127 = __toESM(require_react(), 1);
function CloudArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React127.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React127.createElement("title", {
    id: titleId
  }, title) : null, React127.createElement("path", {
    fillRule: "evenodd",
    d: "M5.5 17a4.5 4.5 0 0 1-1.44-8.765 4.5 4.5 0 0 1 8.302-3.046 3.5 3.5 0 0 1 4.504 4.272A4 4 0 0 1 15 17H5.5Zm5.25-9.25a.75.75 0 0 0-1.5 0v4.59l-1.95-2.1a.75.75 0 1 0-1.1 1.02l3.25 3.5a.75.75 0 0 0 1.1 0l3.25-3.5a.75.75 0 1 0-1.1-1.02l-1.95 2.1V7.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef127 = React127.forwardRef(CloudArrowDownIcon);
var CloudArrowDownIcon_default = ForwardRef127;

// node_modules/@heroicons/react/20/solid/esm/CloudArrowUpIcon.js
var React128 = __toESM(require_react(), 1);
function CloudArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React128.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React128.createElement("title", {
    id: titleId
  }, title) : null, React128.createElement("path", {
    fillRule: "evenodd",
    d: "M5.5 17a4.5 4.5 0 0 1-1.44-8.765 4.5 4.5 0 0 1 8.302-3.046 3.5 3.5 0 0 1 4.504 4.272A4 4 0 0 1 15 17H5.5Zm3.75-2.75a.75.75 0 0 0 1.5 0V9.66l1.95 2.1a.75.75 0 1 0 1.1-1.02l-3.25-3.5a.75.75 0 0 0-1.1 0l-3.25 3.5a.75.75 0 1 0 1.1 1.02l1.95-2.1v4.59Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef128 = React128.forwardRef(CloudArrowUpIcon);
var CloudArrowUpIcon_default = ForwardRef128;

// node_modules/@heroicons/react/20/solid/esm/CloudIcon.js
var React129 = __toESM(require_react(), 1);
function CloudIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React129.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React129.createElement("title", {
    id: titleId
  }, title) : null, React129.createElement("path", {
    d: "M1 12.5A4.5 4.5 0 0 0 5.5 17H15a4 4 0 0 0 1.866-7.539 3.504 3.504 0 0 0-4.504-4.272A4.5 4.5 0 0 0 4.06 8.235 4.502 4.502 0 0 0 1 12.5Z"
  }));
}
var ForwardRef129 = React129.forwardRef(CloudIcon);
var CloudIcon_default = ForwardRef129;

// node_modules/@heroicons/react/20/solid/esm/CodeBracketSquareIcon.js
var React130 = __toESM(require_react(), 1);
function CodeBracketSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React130.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React130.createElement("title", {
    id: titleId
  }, title) : null, React130.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 2A2.25 2.25 0 0 0 2 4.25v11.5A2.25 2.25 0 0 0 4.25 18h11.5A2.25 2.25 0 0 0 18 15.75V4.25A2.25 2.25 0 0 0 15.75 2H4.25Zm4.03 6.28a.75.75 0 0 0-1.06-1.06L4.97 9.47a.75.75 0 0 0 0 1.06l2.25 2.25a.75.75 0 0 0 1.06-1.06L6.56 10l1.72-1.72Zm4.5-1.06a.75.75 0 1 0-1.06 1.06L13.44 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06l2.25-2.25a.75.75 0 0 0 0-1.06l-2.25-2.25Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef130 = React130.forwardRef(CodeBracketSquareIcon);
var CodeBracketSquareIcon_default = ForwardRef130;

// node_modules/@heroicons/react/20/solid/esm/CodeBracketIcon.js
var React131 = __toESM(require_react(), 1);
function CodeBracketIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React131.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React131.createElement("title", {
    id: titleId
  }, title) : null, React131.createElement("path", {
    fillRule: "evenodd",
    d: "M6.28 5.22a.75.75 0 0 1 0 1.06L2.56 10l3.72 3.72a.75.75 0 0 1-1.06 1.06L.97 10.53a.75.75 0 0 1 0-1.06l4.25-4.25a.75.75 0 0 1 1.06 0Zm7.44 0a.75.75 0 0 1 1.06 0l4.25 4.25a.75.75 0 0 1 0 1.06l-4.25 4.25a.75.75 0 0 1-1.06-1.06L17.44 10l-3.72-3.72a.75.75 0 0 1 0-1.06ZM11.377 2.011a.75.75 0 0 1 .612.867l-2.5 14.5a.75.75 0 0 1-1.478-.255l2.5-14.5a.75.75 0 0 1 .866-.612Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef131 = React131.forwardRef(CodeBracketIcon);
var CodeBracketIcon_default = ForwardRef131;

// node_modules/@heroicons/react/20/solid/esm/Cog6ToothIcon.js
var React132 = __toESM(require_react(), 1);
function Cog6ToothIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React132.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React132.createElement("title", {
    id: titleId
  }, title) : null, React132.createElement("path", {
    fillRule: "evenodd",
    d: "M7.84 1.804A1 1 0 0 1 8.82 1h2.36a1 1 0 0 1 .98.804l.331 1.652a6.993 6.993 0 0 1 1.929 1.115l1.598-.54a1 1 0 0 1 1.186.447l1.18 2.044a1 1 0 0 1-.205 1.251l-1.267 1.113a7.047 7.047 0 0 1 0 2.228l1.267 1.113a1 1 0 0 1 .206 1.25l-1.18 2.045a1 1 0 0 1-1.187.447l-1.598-.54a6.993 6.993 0 0 1-1.929 1.115l-.33 1.652a1 1 0 0 1-.98.804H8.82a1 1 0 0 1-.98-.804l-.331-1.652a6.993 6.993 0 0 1-1.929-1.115l-1.598.54a1 1 0 0 1-1.186-.447l-1.18-2.044a1 1 0 0 1 .205-1.251l1.267-1.114a7.05 7.05 0 0 1 0-2.227L1.821 7.773a1 1 0 0 1-.206-1.25l1.18-2.045a1 1 0 0 1 1.187-.447l1.598.54A6.992 6.992 0 0 1 7.51 3.456l.33-1.652ZM10 13a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef132 = React132.forwardRef(Cog6ToothIcon);
var Cog6ToothIcon_default = ForwardRef132;

// node_modules/@heroicons/react/20/solid/esm/Cog8ToothIcon.js
var React133 = __toESM(require_react(), 1);
function Cog8ToothIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React133.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React133.createElement("title", {
    id: titleId
  }, title) : null, React133.createElement("path", {
    fillRule: "evenodd",
    d: "M8.34 1.804A1 1 0 0 1 9.32 1h1.36a1 1 0 0 1 .98.804l.295 1.473c.497.144.971.342 1.416.587l1.25-.834a1 1 0 0 1 1.262.125l.962.962a1 1 0 0 1 .125 1.262l-.834 1.25c.245.445.443.919.587 1.416l1.473.294a1 1 0 0 1 .804.98v1.361a1 1 0 0 1-.804.98l-1.473.295a6.95 6.95 0 0 1-.587 1.416l.834 1.25a1 1 0 0 1-.125 1.262l-.962.962a1 1 0 0 1-1.262.125l-1.25-.834a6.953 6.953 0 0 1-1.416.587l-.294 1.473a1 1 0 0 1-.98.804H9.32a1 1 0 0 1-.98-.804l-.295-1.473a6.957 6.957 0 0 1-1.416-.587l-1.25.834a1 1 0 0 1-1.262-.125l-.962-.962a1 1 0 0 1-.125-1.262l.834-1.25a6.957 6.957 0 0 1-.587-1.416l-1.473-.294A1 1 0 0 1 1 10.68V9.32a1 1 0 0 1 .804-.98l1.473-.295c.144-.497.342-.971.587-1.416l-.834-1.25a1 1 0 0 1 .125-1.262l.962-.962A1 1 0 0 1 5.38 3.03l1.25.834a6.957 6.957 0 0 1 1.416-.587l.294-1.473ZM13 10a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef133 = React133.forwardRef(Cog8ToothIcon);
var Cog8ToothIcon_default = ForwardRef133;

// node_modules/@heroicons/react/20/solid/esm/CogIcon.js
var React134 = __toESM(require_react(), 1);
function CogIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React134.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React134.createElement("title", {
    id: titleId
  }, title) : null, React134.createElement("path", {
    d: "M13.024 9.25c.47 0 .827-.433.637-.863a4 4 0 0 0-4.094-2.364c-.468.05-.665.576-.43.984l1.08 1.868a.75.75 0 0 0 .649.375h2.158ZM7.84 7.758c-.236-.408-.79-.5-1.068-.12A3.982 3.982 0 0 0 6 10c0 .884.287 1.7.772 2.363.278.38.832.287 1.068-.12l1.078-1.868a.75.75 0 0 0 0-.75L7.839 7.758ZM9.138 12.993c-.235.408-.039.934.43.984a4 4 0 0 0 4.094-2.364c.19-.43-.168-.863-.638-.863h-2.158a.75.75 0 0 0-.65.375l-1.078 1.868Z"
  }), React134.createElement("path", {
    fillRule: "evenodd",
    d: "m14.13 4.347.644-1.117a.75.75 0 0 0-1.299-.75l-.644 1.116a6.954 6.954 0 0 0-2.081-.556V1.75a.75.75 0 0 0-1.5 0v1.29a6.954 6.954 0 0 0-2.081.556L6.525 2.48a.75.75 0 1 0-1.3.75l.645 1.117A7.04 7.04 0 0 0 4.347 5.87L3.23 5.225a.75.75 0 1 0-.75 1.3l1.116.644A6.954 6.954 0 0 0 3.04 9.25H1.75a.75.75 0 0 0 0 1.5h1.29c.078.733.27 1.433.556 2.081l-1.116.645a.75.75 0 1 0 .75 1.298l1.117-.644a7.04 7.04 0 0 0 1.523 1.523l-.645 1.117a.75.75 0 1 0 1.3.75l.644-1.116a6.954 6.954 0 0 0 2.081.556v1.29a.75.75 0 0 0 1.5 0v-1.29a6.954 6.954 0 0 0 2.081-.556l.645 1.116a.75.75 0 0 0 1.299-.75l-.645-1.117a7.042 7.042 0 0 0 1.523-1.523l1.117.644a.75.75 0 0 0 .75-1.298l-1.116-.645a6.954 6.954 0 0 0 .556-2.081h1.29a.75.75 0 0 0 0-1.5h-1.29a6.954 6.954 0 0 0-.556-2.081l1.116-.644a.75.75 0 0 0-.75-1.3l-1.117.645a7.04 7.04 0 0 0-1.524-1.523ZM10 4.5a5.475 5.475 0 0 0-2.781.754A5.527 5.527 0 0 0 5.22 7.277 5.475 5.475 0 0 0 4.5 10a5.475 5.475 0 0 0 .752 2.777 5.527 5.527 0 0 0 2.028 2.004c.802.458 1.73.719 2.72.719a5.474 5.474 0 0 0 2.78-.753 5.527 5.527 0 0 0 2.001-2.027c.458-.802.719-1.73.719-2.72a5.475 5.475 0 0 0-.753-2.78 5.528 5.528 0 0 0-2.028-2.002A5.475 5.475 0 0 0 10 4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef134 = React134.forwardRef(CogIcon);
var CogIcon_default = ForwardRef134;

// node_modules/@heroicons/react/20/solid/esm/CommandLineIcon.js
var React135 = __toESM(require_react(), 1);
function CommandLineIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React135.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React135.createElement("title", {
    id: titleId
  }, title) : null, React135.createElement("path", {
    fillRule: "evenodd",
    d: "M3.25 3A2.25 2.25 0 0 0 1 5.25v9.5A2.25 2.25 0 0 0 3.25 17h13.5A2.25 2.25 0 0 0 19 14.75v-9.5A2.25 2.25 0 0 0 16.75 3H3.25Zm.943 8.752a.75.75 0 0 1 .055-1.06L6.128 9l-1.88-1.693a.75.75 0 1 1 1.004-1.114l2.5 2.25a.75.75 0 0 1 0 1.114l-2.5 2.25a.75.75 0 0 1-1.06-.055ZM9.75 10.25a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5h-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef135 = React135.forwardRef(CommandLineIcon);
var CommandLineIcon_default = ForwardRef135;

// node_modules/@heroicons/react/20/solid/esm/ComputerDesktopIcon.js
var React136 = __toESM(require_react(), 1);
function ComputerDesktopIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React136.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React136.createElement("title", {
    id: titleId
  }, title) : null, React136.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.25A2.25 2.25 0 0 1 4.25 2h11.5A2.25 2.25 0 0 1 18 4.25v8.5A2.25 2.25 0 0 1 15.75 15h-3.105a3.501 3.501 0 0 0 1.1 1.677A.75.75 0 0 1 13.26 18H6.74a.75.75 0 0 1-.484-1.323A3.501 3.501 0 0 0 7.355 15H4.25A2.25 2.25 0 0 1 2 12.75v-8.5Zm1.5 0a.75.75 0 0 1 .75-.75h11.5a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-.75.75H4.25a.75.75 0 0 1-.75-.75v-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef136 = React136.forwardRef(ComputerDesktopIcon);
var ComputerDesktopIcon_default = ForwardRef136;

// node_modules/@heroicons/react/20/solid/esm/CpuChipIcon.js
var React137 = __toESM(require_react(), 1);
function CpuChipIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React137.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React137.createElement("title", {
    id: titleId
  }, title) : null, React137.createElement("path", {
    d: "M14 6H6v8h8V6Z"
  }), React137.createElement("path", {
    fillRule: "evenodd",
    d: "M9.25 3V1.75a.75.75 0 0 1 1.5 0V3h1.5V1.75a.75.75 0 0 1 1.5 0V3h.5A2.75 2.75 0 0 1 17 5.75v.5h1.25a.75.75 0 0 1 0 1.5H17v1.5h1.25a.75.75 0 0 1 0 1.5H17v1.5h1.25a.75.75 0 0 1 0 1.5H17v.5A2.75 2.75 0 0 1 14.25 17h-.5v1.25a.75.75 0 0 1-1.5 0V17h-1.5v1.25a.75.75 0 0 1-1.5 0V17h-1.5v1.25a.75.75 0 0 1-1.5 0V17h-.5A2.75 2.75 0 0 1 3 14.25v-.5H1.75a.75.75 0 0 1 0-1.5H3v-1.5H1.75a.75.75 0 0 1 0-1.5H3v-1.5H1.75a.75.75 0 0 1 0-1.5H3v-.5A2.75 2.75 0 0 1 5.75 3h.5V1.75a.75.75 0 0 1 1.5 0V3h1.5ZM4.5 5.75c0-.69.56-1.25 1.25-1.25h8.5c.69 0 1.25.56 1.25 1.25v8.5c0 .69-.56 1.25-1.25 1.25h-8.5c-.69 0-1.25-.56-1.25-1.25v-8.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef137 = React137.forwardRef(CpuChipIcon);
var CpuChipIcon_default = ForwardRef137;

// node_modules/@heroicons/react/20/solid/esm/CreditCardIcon.js
var React138 = __toESM(require_react(), 1);
function CreditCardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React138.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React138.createElement("title", {
    id: titleId
  }, title) : null, React138.createElement("path", {
    fillRule: "evenodd",
    d: "M2.5 4A1.5 1.5 0 0 0 1 5.5V6h18v-.5A1.5 1.5 0 0 0 17.5 4h-15ZM19 8.5H1v6A1.5 1.5 0 0 0 2.5 16h15a1.5 1.5 0 0 0 1.5-1.5v-6ZM3 13.25a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5h-1.5a.75.75 0 0 1-.75-.75Zm4.75-.75a.75.75 0 0 0 0 1.5h3.5a.75.75 0 0 0 0-1.5h-3.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef138 = React138.forwardRef(CreditCardIcon);
var CreditCardIcon_default = ForwardRef138;

// node_modules/@heroicons/react/20/solid/esm/CubeTransparentIcon.js
var React139 = __toESM(require_react(), 1);
function CubeTransparentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React139.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React139.createElement("title", {
    id: titleId
  }, title) : null, React139.createElement("path", {
    fillRule: "evenodd",
    d: "M9.638 1.093a.75.75 0 0 1 .724 0l2 1.104a.75.75 0 1 1-.724 1.313L10 2.607l-1.638.903a.75.75 0 1 1-.724-1.313l2-1.104ZM5.403 4.287a.75.75 0 0 1-.295 1.019l-.805.444.805.444a.75.75 0 0 1-.724 1.314L3.5 7.02v.73a.75.75 0 0 1-1.5 0v-2a.75.75 0 0 1 .388-.657l1.996-1.1a.75.75 0 0 1 1.019.294Zm9.194 0a.75.75 0 0 1 1.02-.295l1.995 1.101A.75.75 0 0 1 18 5.75v2a.75.75 0 0 1-1.5 0v-.73l-.884.488a.75.75 0 1 1-.724-1.314l.806-.444-.806-.444a.75.75 0 0 1-.295-1.02ZM7.343 8.284a.75.75 0 0 1 1.02-.294L10 8.893l1.638-.903a.75.75 0 1 1 .724 1.313l-1.612.89v1.557a.75.75 0 0 1-1.5 0v-1.557l-1.612-.89a.75.75 0 0 1-.295-1.019ZM2.75 11.5a.75.75 0 0 1 .75.75v1.557l1.608.887a.75.75 0 0 1-.724 1.314l-1.996-1.101A.75.75 0 0 1 2 14.25v-2a.75.75 0 0 1 .75-.75Zm14.5 0a.75.75 0 0 1 .75.75v2a.75.75 0 0 1-.388.657l-1.996 1.1a.75.75 0 1 1-.724-1.313l1.608-.887V12.25a.75.75 0 0 1 .75-.75Zm-7.25 4a.75.75 0 0 1 .75.75v.73l.888-.49a.75.75 0 0 1 .724 1.313l-2 1.104a.75.75 0 0 1-.724 0l-2-1.104a.75.75 0 1 1 .724-1.313l.888.49v-.73a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef139 = React139.forwardRef(CubeTransparentIcon);
var CubeTransparentIcon_default = ForwardRef139;

// node_modules/@heroicons/react/20/solid/esm/CubeIcon.js
var React140 = __toESM(require_react(), 1);
function CubeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React140.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React140.createElement("title", {
    id: titleId
  }, title) : null, React140.createElement("path", {
    d: "M10.362 1.093a.75.75 0 0 0-.724 0L2.523 5.018 10 9.143l7.477-4.125-7.115-3.925ZM18 6.443l-7.25 4v8.25l6.862-3.786A.75.75 0 0 0 18 14.25V6.443ZM9.25 18.693v-8.25l-7.25-4v7.807a.75.75 0 0 0 .388.657l6.862 3.786Z"
  }));
}
var ForwardRef140 = React140.forwardRef(CubeIcon);
var CubeIcon_default = ForwardRef140;

// node_modules/@heroicons/react/20/solid/esm/CurrencyBangladeshiIcon.js
var React141 = __toESM(require_react(), 1);
function CurrencyBangladeshiIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React141.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React141.createElement("title", {
    id: titleId
  }, title) : null, React141.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a8 8 0 1 0 0 16 8 8 0 0 0 0-16ZM5.94 5.5c.944-.945 2.56-.276 2.56 1.06V8h5.75a.75.75 0 0 1 0 1.5H8.5v4.275c0 .296.144.455.26.499a3.5 3.5 0 0 0 4.402-1.77h-.412a.75.75 0 0 1 0-1.5h.537c.462 0 .887.21 1.156.556.278.355.383.852.184 1.337a5.001 5.001 0 0 1-6.4 2.78C7.376 15.353 7 14.512 7 13.774V9.5H5.75a.75.75 0 0 1 0-1.5H7V6.56l-.22.22a.75.75 0 1 1-1.06-1.06l.22-.22Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef141 = React141.forwardRef(CurrencyBangladeshiIcon);
var CurrencyBangladeshiIcon_default = ForwardRef141;

// node_modules/@heroicons/react/20/solid/esm/CurrencyDollarIcon.js
var React142 = __toESM(require_react(), 1);
function CurrencyDollarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React142.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React142.createElement("title", {
    id: titleId
  }, title) : null, React142.createElement("path", {
    d: "M10.75 10.818v2.614A3.13 3.13 0 0 0 11.888 13c.482-.315.612-.648.612-.875 0-.227-.13-.56-.612-.875a3.13 3.13 0 0 0-1.138-.432ZM8.33 8.62c.**************.**************.46.284.736.363V6.603a2.45 2.45 0 0 0-.35.13c-.14.065-.27.143-.386.233-.377.292-.514.627-.514.909 0 .**************.592.037.051.08.102.128.152Z"
  }), React142.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-8-6a.75.75 0 0 1 .75.75v.316a3.78 3.78 0 0 1 1.653.713c.426.33.744.74.925 1.2a.75.75 0 0 1-1.395.55 1.35 1.35 0 0 0-.447-.563 2.187 2.187 0 0 0-.736-.363V9.3c.698.093 1.383.32 1.959.696.787.514 1.29 1.27 1.29 2.13 0 .86-.504 1.616-1.29 2.13-.576.377-1.261.603-1.96.696v.299a.75.75 0 1 1-1.5 0v-.3c-.697-.092-1.382-.318-1.958-.695-.482-.315-.857-.717-1.078-1.188a.75.75 0 1 1 1.359-.636c.08.173.245.376.54.569.313.205.706.353 1.138.432v-2.748a3.782 3.782 0 0 1-1.653-.713C6.9 9.433 6.5 8.681 6.5 7.875c0-.805.4-1.558 1.097-2.096a3.78 3.78 0 0 1 1.653-.713V4.75A.75.75 0 0 1 10 4Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef142 = React142.forwardRef(CurrencyDollarIcon);
var CurrencyDollarIcon_default = ForwardRef142;

// node_modules/@heroicons/react/20/solid/esm/CurrencyEuroIcon.js
var React143 = __toESM(require_react(), 1);
function CurrencyEuroIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React143.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React143.createElement("title", {
    id: titleId
  }, title) : null, React143.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.798 7.45c.512-.67 1.135-.95 1.702-.95s1.19.28 1.702.95a.75.75 0 0 0 1.192-.91C12.637 5.55 11.596 5 10.5 5s-2.137.55-2.894 1.54A5.205 5.205 0 0 0 6.83 8H5.75a.75.75 0 0 0 0 1.5h.77a6.333 6.333 0 0 0 0 1h-.77a.75.75 0 0 0 0 1.5h1.08c.183.528.442 1.023.776 1.46.757.99 1.798 1.54 2.894 1.54s2.137-.55 2.894-1.54a.75.75 0 0 0-1.192-.91c-.512.67-1.135.95-1.702.95s-1.19-.28-1.702-.95a3.505 3.505 0 0 1-.343-.55h1.795a.75.75 0 0 0 0-1.5H8.026a4.835 4.835 0 0 1 0-1h2.224a.75.75 0 0 0 0-1.5H8.455c.098-.195.212-.38.343-.55Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef143 = React143.forwardRef(CurrencyEuroIcon);
var CurrencyEuroIcon_default = ForwardRef143;

// node_modules/@heroicons/react/20/solid/esm/CurrencyPoundIcon.js
var React144 = __toESM(require_react(), 1);
function CurrencyPoundIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React144.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React144.createElement("title", {
    id: titleId
  }, title) : null, React144.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.732 6.232a2.5 2.5 0 0 1 3.536 0 .75.75 0 1 0 1.06-1.06A4 4 0 0 0 6.5 8v.165c0 .364.034.728.1 1.085h-.35a.75.75 0 0 0 0 1.5h.737a5.25 5.25 0 0 1-.367 3.072l-.055.123a.75.75 0 0 0 .848 1.037l1.272-.283a3.493 3.493 0 0 1 1.604.021 4.992 4.992 0 0 0 2.422 0l.97-.242a.75.75 0 0 0-.363-1.456l-.971.243a3.491 3.491 0 0 1-1.694 0 4.992 4.992 0 0 0-2.258-.038c.19-.811.227-1.651.111-2.477H9.75a.75.75 0 0 0 0-1.5H8.136A4.397 4.397 0 0 1 8 8.165V8c0-.641.244-1.28.732-1.768Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef144 = React144.forwardRef(CurrencyPoundIcon);
var CurrencyPoundIcon_default = ForwardRef144;

// node_modules/@heroicons/react/20/solid/esm/CurrencyRupeeIcon.js
var React145 = __toESM(require_react(), 1);
function CurrencyRupeeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React145.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React145.createElement("title", {
    id: titleId
  }, title) : null, React145.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM6 5.75A.75.75 0 0 1 6.75 5h6.5a.75.75 0 0 1 0 1.5h-2.127c.4.5.683 1.096.807 1.75h1.32a.75.75 0 0 1 0 1.5h-1.32a4.003 4.003 0 0 1-3.404 3.216l1.754 1.754a.75.75 0 0 1-1.06 1.06l-3-3a.75.75 0 0 1 .53-1.28H8c1.12 0 2.067-.736 2.386-1.75H6.75a.75.75 0 0 1 0-1.5h3.636A2.501 2.501 0 0 0 8 6.5H6.75A.75.75 0 0 1 6 5.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef145 = React145.forwardRef(CurrencyRupeeIcon);
var CurrencyRupeeIcon_default = ForwardRef145;

// node_modules/@heroicons/react/20/solid/esm/CurrencyYenIcon.js
var React146 = __toESM(require_react(), 1);
function CurrencyYenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React146.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React146.createElement("title", {
    id: titleId
  }, title) : null, React146.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM7.346 5.294a.75.75 0 0 0-1.192.912L9.056 10H6.75a.75.75 0 0 0 0 1.5h2.5v1h-2.5a.75.75 0 0 0 0 1.5h2.5v1.25a.75.75 0 0 0 1.5 0V14h2.5a.75.75 0 1 0 0-1.5h-2.5v-1h2.5a.75.75 0 1 0 0-1.5h-2.306l2.902-3.794a.75.75 0 1 0-1.192-.912L10 8.765l-2.654-3.47Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef146 = React146.forwardRef(CurrencyYenIcon);
var CurrencyYenIcon_default = ForwardRef146;

// node_modules/@heroicons/react/20/solid/esm/CursorArrowRaysIcon.js
var React147 = __toESM(require_react(), 1);
function CursorArrowRaysIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React147.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React147.createElement("title", {
    id: titleId
  }, title) : null, React147.createElement("path", {
    d: "M10 1a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 10 1ZM5.05 3.05a.75.75 0 0 1 1.06 0l1.062 1.06A.75.75 0 1 1 6.11 5.173L5.05 4.11a.75.75 0 0 1 0-1.06ZM14.95 3.05a.75.75 0 0 1 0 1.06l-1.06 1.062a.75.75 0 0 1-1.062-1.061l1.061-1.06a.75.75 0 0 1 1.06 0ZM3 8a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5h-1.5A.75.75 0 0 1 3 8ZM14 8a.75.75 0 0 1 .75-.75h1.5a.75.75 0 0 1 0 1.5h-1.5A.75.75 0 0 1 14 8ZM7.172 10.828a.75.75 0 0 1 0 1.061L6.11 12.95a.75.75 0 0 1-1.06-1.06l1.06-1.06a.75.75 0 0 1 1.06 0ZM10.766 7.51a.75.75 0 0 0-1.37.365l-.492 6.861a.75.75 0 0 0 1.204.65l1.043-.799.985 3.678a.75.75 0 0 0 1.45-.388l-.978-3.646 1.292.204a.75.75 0 0 0 .74-1.16l-3.874-5.764Z"
  }));
}
var ForwardRef147 = React147.forwardRef(CursorArrowRaysIcon);
var CursorArrowRaysIcon_default = ForwardRef147;

// node_modules/@heroicons/react/20/solid/esm/CursorArrowRippleIcon.js
var React148 = __toESM(require_react(), 1);
function CursorArrowRippleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React148.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React148.createElement("title", {
    id: titleId
  }, title) : null, React148.createElement("path", {
    d: "M6.111 11.89A5.5 5.5 0 1 1 15.501 8 .75.75 0 0 0 17 8a7 7 0 1 0-11.95 ********** 0 0 0 1.06-1.06Z"
  }), React148.createElement("path", {
    d: "M8.232 6.232a2.5 2.5 0 0 0 0 3.536.75.75 0 1 1-1.06 1.06A4 4 0 1 1 14 8a.75.75 0 0 1-1.5 0 2.5 2.5 0 0 0-4.268-1.768Z"
  }), React148.createElement("path", {
    d: "M10.766 7.51a.75.75 0 0 0-1.37.365l-.492 6.861a.75.75 0 0 0 1.204.65l1.043-.799.985 3.678a.75.75 0 0 0 1.45-.388l-.978-3.646 1.292.204a.75.75 0 0 0 .74-1.16l-3.874-5.764Z"
  }));
}
var ForwardRef148 = React148.forwardRef(CursorArrowRippleIcon);
var CursorArrowRippleIcon_default = ForwardRef148;

// node_modules/@heroicons/react/20/solid/esm/DevicePhoneMobileIcon.js
var React149 = __toESM(require_react(), 1);
function DevicePhoneMobileIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React149.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React149.createElement("title", {
    id: titleId
  }, title) : null, React149.createElement("path", {
    d: "M8 16.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z"
  }), React149.createElement("path", {
    fillRule: "evenodd",
    d: "M4 4a3 3 0 0 1 3-3h6a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H7a3 3 0 0 1-3-3V4Zm4-1.5v.75c0 .414.336.75.75.75h2.5a.75.75 0 0 0 .75-.75V2.5h1A1.5 1.5 0 0 1 14.5 4v12a1.5 1.5 0 0 1-1.5 1.5H7A1.5 1.5 0 0 1 5.5 16V4A1.5 1.5 0 0 1 7 2.5h1Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef149 = React149.forwardRef(DevicePhoneMobileIcon);
var DevicePhoneMobileIcon_default = ForwardRef149;

// node_modules/@heroicons/react/20/solid/esm/DeviceTabletIcon.js
var React150 = __toESM(require_react(), 1);
function DeviceTabletIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React150.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React150.createElement("title", {
    id: titleId
  }, title) : null, React150.createElement("path", {
    fillRule: "evenodd",
    d: "M5 1a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V4a3 3 0 0 0-3-3H5ZM3.5 4A1.5 1.5 0 0 1 5 2.5h10A1.5 1.5 0 0 1 16.5 4v12a1.5 1.5 0 0 1-1.5 1.5H5A1.5 1.5 0 0 1 3.5 16V4Zm5.25 11.5a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5h-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef150 = React150.forwardRef(DeviceTabletIcon);
var DeviceTabletIcon_default = ForwardRef150;

// node_modules/@heroicons/react/20/solid/esm/DivideIcon.js
var React151 = __toESM(require_react(), 1);
function DivideIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React151.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React151.createElement("title", {
    id: titleId
  }, title) : null, React151.createElement("path", {
    d: "M11.25 4a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0ZM3 10a.75.75 0 0 1 .75-.75h12.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 10ZM10 17.25a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z"
  }));
}
var ForwardRef151 = React151.forwardRef(DivideIcon);
var DivideIcon_default = ForwardRef151;

// node_modules/@heroicons/react/20/solid/esm/DocumentArrowDownIcon.js
var React152 = __toESM(require_react(), 1);
function DocumentArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React152.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React152.createElement("title", {
    id: titleId
  }, title) : null, React152.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm4.75 6.75a.75.75 0 0 1 1.5 0v2.546l.943-1.048a.75.75 0 0 1 1.114 1.004l-2.25 2.5a.75.75 0 0 1-1.114 0l-2.25-2.5a.75.75 0 1 1 1.114-1.004l.943 1.048V8.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef152 = React152.forwardRef(DocumentArrowDownIcon);
var DocumentArrowDownIcon_default = ForwardRef152;

// node_modules/@heroicons/react/20/solid/esm/DocumentArrowUpIcon.js
var React153 = __toESM(require_react(), 1);
function DocumentArrowUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React153.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React153.createElement("title", {
    id: titleId
  }, title) : null, React153.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm4.75 11.25a.75.75 0 0 0 1.5 0v-2.546l.943 1.048a.75.75 0 1 0 1.114-1.004l-2.25-2.5a.75.75 0 0 0-1.114 0l-2.25 2.5a.75.75 0 1 0 1.114 1.004l.943-1.048v2.546Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef153 = React153.forwardRef(DocumentArrowUpIcon);
var DocumentArrowUpIcon_default = ForwardRef153;

// node_modules/@heroicons/react/20/solid/esm/DocumentChartBarIcon.js
var React154 = __toESM(require_react(), 1);
function DocumentChartBarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React154.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React154.createElement("title", {
    id: titleId
  }, title) : null, React154.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3.5A1.5 1.5 0 0 1 4.5 2h6.879a1.5 1.5 0 0 1 1.06.44l4.122 4.12A1.5 1.5 0 0 1 17 7.622V16.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 3 16.5v-13ZM13.25 9a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5a.75.75 0 0 1 .75-.75Zm-6.5 4a.75.75 0 0 1 .75.75v.5a.75.75 0 0 1-1.5 0v-.5a.75.75 0 0 1 .75-.75Zm4-1.25a.75.75 0 0 0-1.5 0v2.5a.75.75 0 0 0 1.5 0v-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef154 = React154.forwardRef(DocumentChartBarIcon);
var DocumentChartBarIcon_default = ForwardRef154;

// node_modules/@heroicons/react/20/solid/esm/DocumentCheckIcon.js
var React155 = __toESM(require_react(), 1);
function DocumentCheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React155.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React155.createElement("title", {
    id: titleId
  }, title) : null, React155.createElement("path", {
    fillRule: "evenodd",
    d: "M3 3.5A1.5 1.5 0 0 1 4.5 2h6.879a1.5 1.5 0 0 1 1.06.44l4.122 4.12A1.5 1.5 0 0 1 17 7.622V16.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 3 16.5v-13Zm10.857 5.691a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 0 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef155 = React155.forwardRef(DocumentCheckIcon);
var DocumentCheckIcon_default = ForwardRef155;

// node_modules/@heroicons/react/20/solid/esm/DocumentCurrencyBangladeshiIcon.js
var React156 = __toESM(require_react(), 1);
function DocumentCurrencyBangladeshiIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React156.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React156.createElement("title", {
    id: titleId
  }, title) : null, React156.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm1.97 4.53a.75.75 0 0 0 .78.178V8h-1.5a.75.75 0 1 0 0 1.5h1.5v3.098c0 .98.571 2.18 1.837 2.356a4.751 4.751 0 0 0 5.066-********** 0 0 0-.695-1.031H11.75a.75.75 0 0 0 0 1.5h.343a3.241 3.241 0 0 1-2.798.966c-.25-.035-.545-.322-.545-.87V9.5h5.5a.75.75 0 0 0 0-1.5h-5.5V6.415c0-1.19-1.439-1.786-2.28-.945a.75.75 0 0 0 0 1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef156 = React156.forwardRef(DocumentCurrencyBangladeshiIcon);
var DocumentCurrencyBangladeshiIcon_default = ForwardRef156;

// node_modules/@heroicons/react/20/solid/esm/DocumentCurrencyDollarIcon.js
var React157 = __toESM(require_react(), 1);
function DocumentCurrencyDollarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React157.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React157.createElement("title", {
    id: titleId
  }, title) : null, React157.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm6.25 3.75a.75.75 0 0 0-1.5 0v.272c-.418.024-.831.069-1.238.132-.962.15-1.807.882-1.95 1.928-.04.3-.062.607-.062.918 0 1.044.83 1.759 1.708 1.898l1.542.243v2.334a11.214 11.214 0 0 1-2.297-.392.75.75 0 0 0-.405 1.444c.867.243 1.772.397 2.702.451v.272a.75.75 0 0 0 1.5 0v-.272c.419-.024.832-.069 1.239-.132.961-.15 1.807-.882 1.95-1.928.04-.3.061-.607.061-.918 0-1.044-.83-1.759-1.708-1.898L10.75 9.86V7.525c.792.052 1.56.185 2.297.392a.75.75 0 0 0 .406-1.444 12.723 12.723 0 0 0-2.703-.451V5.75ZM8.244 7.636c.33-.052.666-.09 1.006-.111v2.097l-1.308-.206C7.635 9.367 7.5 9.156 7.5 9c0-.243.017-.482.049-.716.042-.309.305-.587.695-.648Zm2.506 5.84v-2.098l1.308.206c.307.049.442.26.442.416 0 .243-.016.482-.048.716-.042.309-.306.587-.695.648-.331.052-.667.09-1.007.111Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef157 = React157.forwardRef(DocumentCurrencyDollarIcon);
var DocumentCurrencyDollarIcon_default = ForwardRef157;

// node_modules/@heroicons/react/20/solid/esm/DocumentCurrencyEuroIcon.js
var React158 = __toESM(require_react(), 1);
function DocumentCurrencyEuroIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React158.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React158.createElement("title", {
    id: titleId
  }, title) : null, React158.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm4.298 6.45c.512-.67 1.135-.95 1.702-.95s1.19.28 1.702.95a.75.75 0 0 0 1.192-.91C12.637 6.55 11.596 6 10.5 6s-2.137.55-2.894 1.54A5.205 5.205 0 0 0 6.83 9H5.75a.75.75 0 0 0 0 1.5h.77a6.333 6.333 0 0 0 0 1h-.77a.75.75 0 0 0 0 1.5h1.08c.183.528.442 1.023.776 1.46.757.99 1.798 1.54 2.894 1.54s2.137-.55 2.894-1.54a.75.75 0 0 0-1.192-.91c-.512.67-1.135.95-1.702.95s-1.19-.28-1.702-.95a3.505 3.505 0 0 1-.343-.55h1.795a.75.75 0 0 0 0-1.5H8.026a4.835 4.835 0 0 1 0-1h2.224a.75.75 0 0 0 0-1.5H8.455c.098-.195.212-.38.343-.55Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef158 = React158.forwardRef(DocumentCurrencyEuroIcon);
var DocumentCurrencyEuroIcon_default = ForwardRef158;

// node_modules/@heroicons/react/20/solid/esm/DocumentCurrencyPoundIcon.js
var React159 = __toESM(require_react(), 1);
function DocumentCurrencyPoundIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React159.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React159.createElement("title", {
    id: titleId
  }, title) : null, React159.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm5 7a1.5 1.5 0 0 1 2.56-********** 0 1 0 1.062-1.061A3 3 0 0 0 8 9v1.25H6.75a.75.75 0 0 0 0 1.5H8v1a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 1 0 0-1.5H9.372c.083-.235.128-.487.128-.75v-1h1.25a.75.75 0 0 0 0-1.5H9.5V9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef159 = React159.forwardRef(DocumentCurrencyPoundIcon);
var DocumentCurrencyPoundIcon_default = ForwardRef159;

// node_modules/@heroicons/react/20/solid/esm/DocumentCurrencyRupeeIcon.js
var React160 = __toESM(require_react(), 1);
function DocumentCurrencyRupeeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React160.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React160.createElement("title", {
    id: titleId
  }, title) : null, React160.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5ZM6 5.75A.75.75 0 0 1 6.75 5h6.5a.75.75 0 0 1 0 1.5h-2.127c.4.5.683 1.096.807 1.75h1.32a.75.75 0 0 1 0 1.5h-1.32a4.003 4.003 0 0 1-3.404 3.216l1.754 1.754a.75.75 0 0 1-1.06 1.06l-3-3a.75.75 0 0 1 .53-1.28H8c1.12 0 2.067-.736 2.386-1.75H6.75a.75.75 0 0 1 0-1.5h3.636A2.501 2.501 0 0 0 8 6.5H6.75A.75.75 0 0 1 6 5.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef160 = React160.forwardRef(DocumentCurrencyRupeeIcon);
var DocumentCurrencyRupeeIcon_default = ForwardRef160;

// node_modules/@heroicons/react/20/solid/esm/DocumentCurrencyYenIcon.js
var React161 = __toESM(require_react(), 1);
function DocumentCurrencyYenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React161.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React161.createElement("title", {
    id: titleId
  }, title) : null, React161.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm3.846 4.294a.75.75 0 0 0-1.192.912L9.056 10H6.75a.75.75 0 0 0 0 1.5h2.5v1h-2.5a.75.75 0 0 0 0 1.5h2.5v1.25a.75.75 0 1 0 1.5 0V14h2.5a.75.75 0 1 0 0-1.5h-2.5v-1h2.5a.75.75 0 1 0 0-1.5h-2.306l1.902-2.794a.75.75 0 0 0-1.192-.912L10 8.765l-1.654-2.47Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef161 = React161.forwardRef(DocumentCurrencyYenIcon);
var DocumentCurrencyYenIcon_default = ForwardRef161;

// node_modules/@heroicons/react/20/solid/esm/DocumentDuplicateIcon.js
var React162 = __toESM(require_react(), 1);
function DocumentDuplicateIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React162.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React162.createElement("title", {
    id: titleId
  }, title) : null, React162.createElement("path", {
    d: "M7 3.5A1.5 1.5 0 0 1 8.5 2h3.879a1.5 1.5 0 0 1 1.06.44l3.122 3.12A1.5 1.5 0 0 1 17 6.622V12.5a1.5 1.5 0 0 1-1.5 1.5h-1v-3.379a3 3 0 0 0-.879-2.121L10.5 5.379A3 3 0 0 0 8.379 4.5H7v-1Z"
  }), React162.createElement("path", {
    d: "M4.5 6A1.5 1.5 0 0 0 3 7.5v9A1.5 1.5 0 0 0 4.5 18h7a1.5 1.5 0 0 0 1.5-1.5v-5.879a1.5 1.5 0 0 0-.44-1.06L9.44 6.439A1.5 1.5 0 0 0 8.378 6H4.5Z"
  }));
}
var ForwardRef162 = React162.forwardRef(DocumentDuplicateIcon);
var DocumentDuplicateIcon_default = ForwardRef162;

// node_modules/@heroicons/react/20/solid/esm/DocumentMagnifyingGlassIcon.js
var React163 = __toESM(require_react(), 1);
function DocumentMagnifyingGlassIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React163.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React163.createElement("title", {
    id: titleId
  }, title) : null, React163.createElement("path", {
    d: "M8 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0Z"
  }), React163.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm5 5a3 3 0 1 0 1.524 5.585l1.196 1.195a.75.75 0 1 0 1.06-1.06l-1.195-1.196A3 3 0 0 0 9.5 7Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef163 = React163.forwardRef(DocumentMagnifyingGlassIcon);
var DocumentMagnifyingGlassIcon_default = ForwardRef163;

// node_modules/@heroicons/react/20/solid/esm/DocumentMinusIcon.js
var React164 = __toESM(require_react(), 1);
function DocumentMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React164.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React164.createElement("title", {
    id: titleId
  }, title) : null, React164.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm7.75 9.75a.75.75 0 0 0 0-1.5h-4.5a.75.75 0 0 0 0 1.5h4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef164 = React164.forwardRef(DocumentMinusIcon);
var DocumentMinusIcon_default = ForwardRef164;

// node_modules/@heroicons/react/20/solid/esm/DocumentPlusIcon.js
var React165 = __toESM(require_react(), 1);
function DocumentPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React165.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React165.createElement("title", {
    id: titleId
  }, title) : null, React165.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5ZM10 8a.75.75 0 0 1 .75.75v1.5h1.5a.75.75 0 0 1 0 1.5h-1.5v1.5a.75.75 0 0 1-1.5 0v-1.5h-1.5a.75.75 0 0 1 0-1.5h1.5v-1.5A.75.75 0 0 1 10 8Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef165 = React165.forwardRef(DocumentPlusIcon);
var DocumentPlusIcon_default = ForwardRef165;

// node_modules/@heroicons/react/20/solid/esm/DocumentTextIcon.js
var React166 = __toESM(require_react(), 1);
function DocumentTextIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React166.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React166.createElement("title", {
    id: titleId
  }, title) : null, React166.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef166 = React166.forwardRef(DocumentTextIcon);
var DocumentTextIcon_default = ForwardRef166;

// node_modules/@heroicons/react/20/solid/esm/DocumentIcon.js
var React167 = __toESM(require_react(), 1);
function DocumentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React167.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React167.createElement("title", {
    id: titleId
  }, title) : null, React167.createElement("path", {
    d: "M3 3.5A1.5 1.5 0 0 1 4.5 2h6.879a1.5 1.5 0 0 1 1.06.44l4.122 4.12A1.5 1.5 0 0 1 17 7.622V16.5a1.5 1.5 0 0 1-1.5 1.5h-11A1.5 1.5 0 0 1 3 16.5v-13Z"
  }));
}
var ForwardRef167 = React167.forwardRef(DocumentIcon);
var DocumentIcon_default = ForwardRef167;

// node_modules/@heroicons/react/20/solid/esm/EllipsisHorizontalCircleIcon.js
var React168 = __toESM(require_react(), 1);
function EllipsisHorizontalCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React168.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React168.createElement("title", {
    id: titleId
  }, title) : null, React168.createElement("path", {
    fillRule: "evenodd",
    d: "M2 10a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm8 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Zm-3-1a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm7 1a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef168 = React168.forwardRef(EllipsisHorizontalCircleIcon);
var EllipsisHorizontalCircleIcon_default = ForwardRef168;

// node_modules/@heroicons/react/20/solid/esm/EllipsisHorizontalIcon.js
var React169 = __toESM(require_react(), 1);
function EllipsisHorizontalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React169.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React169.createElement("title", {
    id: titleId
  }, title) : null, React169.createElement("path", {
    d: "M3 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM8.5 10a1.5 1.5 0 1 1 3 0 1.5 1.5 0 0 1-3 0ZM15.5 8.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Z"
  }));
}
var ForwardRef169 = React169.forwardRef(EllipsisHorizontalIcon);
var EllipsisHorizontalIcon_default = ForwardRef169;

// node_modules/@heroicons/react/20/solid/esm/EllipsisVerticalIcon.js
var React170 = __toESM(require_react(), 1);
function EllipsisVerticalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React170.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React170.createElement("title", {
    id: titleId
  }, title) : null, React170.createElement("path", {
    d: "M10 3a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM10 8.5a1.5 1.5 0 1 1 0 3 1.5 1.5 0 0 1 0-3ZM11.5 15.5a1.5 1.5 0 1 0-3 0 1.5 1.5 0 0 0 3 0Z"
  }));
}
var ForwardRef170 = React170.forwardRef(EllipsisVerticalIcon);
var EllipsisVerticalIcon_default = ForwardRef170;

// node_modules/@heroicons/react/20/solid/esm/EnvelopeOpenIcon.js
var React171 = __toESM(require_react(), 1);
function EnvelopeOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React171.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React171.createElement("title", {
    id: titleId
  }, title) : null, React171.createElement("path", {
    fillRule: "evenodd",
    d: "M2.106 6.447A2 2 0 0 0 1 8.237V16a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.236a2 2 0 0 0-1.106-1.789l-7-3.5a2 2 0 0 0-1.788 0l-7 3.5Zm1.48 4.007a.75.75 0 0 0-.671 1.342l5.855 2.928a2.75 2.75 0 0 0 2.46 0l5.852-2.927a.75.75 0 1 0-.67-1.341l-5.853 2.926a1.25 1.25 0 0 1-1.118 0l-5.856-2.928Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef171 = React171.forwardRef(EnvelopeOpenIcon);
var EnvelopeOpenIcon_default = ForwardRef171;

// node_modules/@heroicons/react/20/solid/esm/EnvelopeIcon.js
var React172 = __toESM(require_react(), 1);
function EnvelopeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React172.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React172.createElement("title", {
    id: titleId
  }, title) : null, React172.createElement("path", {
    d: "M3 4a2 2 0 0 0-2 2v1.161l8.441 4.221a1.25 1.25 0 0 0 1.118 0L19 7.162V6a2 2 0 0 0-2-2H3Z"
  }), React172.createElement("path", {
    d: "m19 8.839-7.77 3.885a2.75 2.75 0 0 1-2.46 0L1 8.839V14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V8.839Z"
  }));
}
var ForwardRef172 = React172.forwardRef(EnvelopeIcon);
var EnvelopeIcon_default = ForwardRef172;

// node_modules/@heroicons/react/20/solid/esm/EqualsIcon.js
var React173 = __toESM(require_react(), 1);
function EqualsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React173.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React173.createElement("title", {
    id: titleId
  }, title) : null, React173.createElement("path", {
    d: "M3.75 6a.75.75 0 0 0 0 1.5h12.5a.75.75 0 0 0 0-1.5H3.75ZM3.75 13.5a.75.75 0 0 0 0 1.5h12.5a.75.75 0 0 0 0-1.5H3.75Z"
  }));
}
var ForwardRef173 = React173.forwardRef(EqualsIcon);
var EqualsIcon_default = ForwardRef173;

// node_modules/@heroicons/react/20/solid/esm/ExclamationCircleIcon.js
var React174 = __toESM(require_react(), 1);
function ExclamationCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React174.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React174.createElement("title", {
    id: titleId
  }, title) : null, React174.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-8-5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0v-4.5A.75.75 0 0 1 10 5Zm0 10a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef174 = React174.forwardRef(ExclamationCircleIcon);
var ExclamationCircleIcon_default = ForwardRef174;

// node_modules/@heroicons/react/20/solid/esm/ExclamationTriangleIcon.js
var React175 = __toESM(require_react(), 1);
function ExclamationTriangleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React175.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React175.createElement("title", {
    id: titleId
  }, title) : null, React175.createElement("path", {
    fillRule: "evenodd",
    d: "M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495ZM10 5a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 10 5Zm0 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef175 = React175.forwardRef(ExclamationTriangleIcon);
var ExclamationTriangleIcon_default = ForwardRef175;

// node_modules/@heroicons/react/20/solid/esm/EyeDropperIcon.js
var React176 = __toESM(require_react(), 1);
function EyeDropperIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React176.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React176.createElement("title", {
    id: titleId
  }, title) : null, React176.createElement("path", {
    fillRule: "evenodd",
    d: "M12.1 3.667a3.502 3.502 0 1 1 6.782 1.738 3.487 3.487 0 0 1-.907 1.57 3.495 3.495 0 0 1-1.617.919L16 7.99V10a.75.75 0 0 1-.22.53l-.25.25a.75.75 0 0 1-1.06 0l-.845-.844L7.22 16.34A2.25 2.25 0 0 1 5.629 17H5.12a.75.75 0 0 0-.53.22l-1.56 1.56a.75.75 0 0 1-1.061 0l-.75-.75a.75.75 0 0 1 0-1.06l1.56-1.561a.75.75 0 0 0 .22-.53v-.508c0-.596.237-1.169.659-1.59l6.405-6.406-.844-.845a.75.75 0 0 1 0-1.06l.25-.25A.75.75 0 0 1 10 4h2.01l.09-.333ZM4.72 13.84l6.405-6.405 1.44 1.439-6.406 6.405a.75.75 0 0 1-.53.22H5.12c-.258 0-.511.044-.75.129a2.25 2.25 0 0 0 .129-.75v-.508a.75.75 0 0 1 .22-.53Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef176 = React176.forwardRef(EyeDropperIcon);
var EyeDropperIcon_default = ForwardRef176;

// node_modules/@heroicons/react/20/solid/esm/EyeSlashIcon.js
var React177 = __toESM(require_react(), 1);
function EyeSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React177.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React177.createElement("title", {
    id: titleId
  }, title) : null, React177.createElement("path", {
    fillRule: "evenodd",
    d: "M3.28 2.22a.75.75 0 0 0-1.06 1.06l14.5 14.5a.75.75 0 1 0 1.06-1.06l-1.745-1.745a10.029 10.029 0 0 0 3.3-4.38 1.651 1.651 0 0 0 0-1.185A10.004 10.004 0 0 0 9.999 3a9.956 9.956 0 0 0-4.744 1.194L3.28 2.22ZM7.752 6.69l1.092 1.092a2.5 2.5 0 0 1 3.374 3.373l1.091 1.092a4 4 0 0 0-5.557-5.557Z",
    clipRule: "evenodd"
  }), React177.createElement("path", {
    d: "m10.748 13.93 2.523 2.523a9.987 9.987 0 0 1-3.27.547c-4.258 0-7.894-2.66-9.337-6.41a1.651 1.651 0 0 1 0-1.186A10.007 10.007 0 0 1 2.839 6.02L6.07 9.252a4 4 0 0 0 4.678 4.678Z"
  }));
}
var ForwardRef177 = React177.forwardRef(EyeSlashIcon);
var EyeSlashIcon_default = ForwardRef177;

// node_modules/@heroicons/react/20/solid/esm/EyeIcon.js
var React178 = __toESM(require_react(), 1);
function EyeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React178.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React178.createElement("title", {
    id: titleId
  }, title) : null, React178.createElement("path", {
    d: "M10 12.5a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5Z"
  }), React178.createElement("path", {
    fillRule: "evenodd",
    d: "M.664 10.59a1.651 1.651 0 0 1 0-1.186A10.004 10.004 0 0 1 10 3c4.257 0 7.893 2.66 9.336 6.41.147.381.146.804 0 1.186A10.004 10.004 0 0 1 10 17c-4.257 0-7.893-2.66-9.336-6.41ZM14 10a4 4 0 1 1-8 0 4 4 0 0 1 8 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef178 = React178.forwardRef(EyeIcon);
var EyeIcon_default = ForwardRef178;

// node_modules/@heroicons/react/20/solid/esm/FaceFrownIcon.js
var React179 = __toESM(require_react(), 1);
function FaceFrownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React179.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React179.createElement("title", {
    id: titleId
  }, title) : null, React179.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm-3.536-3.475a.75.75 0 0 0 1.061 0 3.5 3.5 0 0 1 4.95 0 .75.75 0 1 0 1.06-1.06 5 5 0 0 0-7.07 0 .75.75 0 0 0 0 1.06ZM9 8.5c0 .828-.448 1.5-1 1.5s-1-.672-1-1.5S7.448 7 8 7s1 .672 1 1.5Zm3 1.5c.552 0 1-.672 1-1.5S12.552 7 12 7s-1 .672-1 1.5.448 1.5 1 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef179 = React179.forwardRef(FaceFrownIcon);
var FaceFrownIcon_default = ForwardRef179;

// node_modules/@heroicons/react/20/solid/esm/FaceSmileIcon.js
var React180 = __toESM(require_react(), 1);
function FaceSmileIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React180.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React180.createElement("title", {
    id: titleId
  }, title) : null, React180.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm3.536-4.464a.75.75 0 1 0-1.061-1.061 3.5 3.5 0 0 1-4.95 0 .75.75 0 0 0-1.06 1.06 5 5 0 0 0 7.07 0ZM9 8.5c0 .828-.448 1.5-1 1.5s-1-.672-1-1.5S7.448 7 8 7s1 .672 1 1.5Zm3 1.5c.552 0 1-.672 1-1.5S12.552 7 12 7s-1 .672-1 1.5.448 1.5 1 1.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef180 = React180.forwardRef(FaceSmileIcon);
var FaceSmileIcon_default = ForwardRef180;

// node_modules/@heroicons/react/20/solid/esm/FilmIcon.js
var React181 = __toESM(require_react(), 1);
function FilmIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React181.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React181.createElement("title", {
    id: titleId
  }, title) : null, React181.createElement("path", {
    fillRule: "evenodd",
    d: "M1 4.75C1 3.784 1.784 3 2.75 3h14.5c.966 0 1.75.784 1.75 1.75v10.515a1.75 1.75 0 0 1-1.75 1.75h-1.5c-.078 0-.155-.005-.23-.015H4.48c-.075.01-.152.015-.23.015h-1.5A1.75 1.75 0 0 1 1 15.265V4.75Zm16.5 7.385V11.01a.25.25 0 0 0-.25-.25h-1.5a.25.25 0 0 0-.25.25v1.125c0 .*************.25h1.5a.25.25 0 0 0 .25-.25Zm0 2.005a.25.25 0 0 0-.25-.25h-1.5a.25.25 0 0 0-.25.25v1.125c0 .108.069.2.165.235h1.585a.25.25 0 0 0 .25-.25v-1.11Zm-15 1.11v-1.11a.25.25 0 0 1 .25-.25h1.5a.25.25 0 0 1 .25.25v1.125a.25.25 0 0 1-.164.235H2.75a.25.25 0 0 1-.25-.25Zm2-4.24v1.125a.25.25 0 0 1-.25.25h-1.5a.25.25 0 0 1-.25-.25V11.01a.25.25 0 0 1 .25-.25h1.5a.25.25 0 0 1 .25.25Zm13-2.005V7.88a.25.25 0 0 0-.25-.25h-1.5a.25.25 0 0 0-.25.25v1.125c0 .*************.25h1.5a.25.25 0 0 0 .25-.25ZM4.25 7.63a.25.25 0 0 1 .25.25v1.125a.25.25 0 0 1-.25.25h-1.5a.25.25 0 0 1-.25-.25V7.88a.25.25 0 0 1 .25-.25h1.5Zm0-3.13a.25.25 0 0 1 .25.25v1.125a.25.25 0 0 1-.25.25h-1.5a.25.25 0 0 1-.25-.25V4.75a.25.25 0 0 1 .25-.25h1.5Zm11.5 1.625a.25.25 0 0 1-.25-.25V4.75a.25.25 0 0 1 .25-.25h1.5a.25.25 0 0 1 .25.25v1.125a.25.25 0 0 1-.25.25h-1.5Zm-9 3.125a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef181 = React181.forwardRef(FilmIcon);
var FilmIcon_default = ForwardRef181;

// node_modules/@heroicons/react/20/solid/esm/FingerPrintIcon.js
var React182 = __toESM(require_react(), 1);
function FingerPrintIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React182.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React182.createElement("title", {
    id: titleId
  }, title) : null, React182.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2.5c-1.31 0-2.526.386-3.546 1.051a.75.75 0 0 1-.82-1.256A8 8 0 0 1 18 9a22.47 22.47 0 0 1-1.228 7.351.75.75 0 1 1-1.417-.49A20.97 20.97 0 0 0 16.5 9 6.5 6.5 0 0 0 10 2.5ZM4.333 4.416a.75.75 0 0 1 .218 1.038A6.466 6.466 0 0 0 3.5 9a7.966 7.966 0 0 1-1.293 4.362.75.75 0 0 1-1.257-.819A6.466 6.466 0 0 0 2 9c0-1.61.476-3.11 1.295-4.365a.75.75 0 0 1 1.038-.219ZM10 6.12a3 3 0 0 0-3.001 3.041 11.455 11.455 0 0 1-2.697 ********** 0 0 1-1.148-.965A9.957 9.957 0 0 0 5.5 9c0-.028.002-.055.004-.082a4.5 4.5 0 0 1 8.996.084V9.15l-.005.297a.75.75 0 1 1-1.5-.034c.003-.11.004-.219.005-.328a3 3 0 0 0-3-2.965Zm0 2.13a.75.75 0 0 1 .75.75c0 3.51-1.187 6.745-3.181 9.323a.75.75 0 1 1-1.186-.918A13.687 13.687 0 0 0 9.25 9a.75.75 0 0 1 .75-.75Zm3.529 3.698a.75.75 0 0 1 .584.885 18.883 18.883 0 0 1-2.257 ********** 0 1 1-1.29-.764 17.386 17.386 0 0 0 2.078-5.377.75.75 0 0 1 .885-.584Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef182 = React182.forwardRef(FingerPrintIcon);
var FingerPrintIcon_default = ForwardRef182;

// node_modules/@heroicons/react/20/solid/esm/FireIcon.js
var React183 = __toESM(require_react(), 1);
function FireIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React183.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React183.createElement("title", {
    id: titleId
  }, title) : null, React183.createElement("path", {
    fillRule: "evenodd",
    d: "M13.5 4.938a7 7 0 1 1-9.006 1.737c.202-.257.59-.218.793.039.278.352.594.672.943.954.332.269.786-.049.773-.476a5.977 5.977 0 0 1 .572-2.759 6.026 6.026 0 0 1 2.486-2.665c.247-.14.55-.016.677.238A6.967 6.967 0 0 0 13.5 4.938ZM14 12a4 4 0 0 1-4 4c-1.913 0-3.52-1.398-3.91-3.182-.093-.429.44-.643.814-.413a4.043 4.043 0 0 0 1.601.564c.303.038.531-.24.51-.544a5.975 5.975 0 0 1 1.315-4.192.447.447 0 0 1 .431-.16A4.001 4.001 0 0 1 14 12Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef183 = React183.forwardRef(FireIcon);
var FireIcon_default = ForwardRef183;

// node_modules/@heroicons/react/20/solid/esm/FlagIcon.js
var React184 = __toESM(require_react(), 1);
function FlagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React184.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React184.createElement("title", {
    id: titleId
  }, title) : null, React184.createElement("path", {
    d: "M3.5 2.75a.75.75 0 0 0-1.5 0v14.5a.75.75 0 0 0 1.5 0v-4.392l1.657-.348a6.449 6.449 0 0 1 4.271.572 7.948 7.948 0 0 0 5.965.524l2.078-.64A.75.75 0 0 0 18 12.25v-8.5a.75.75 0 0 0-.904-.734l-2.38.501a7.25 7.25 0 0 1-4.186-.363l-.502-.2a8.75 8.75 0 0 0-5.053-.439l-1.475.31V2.75Z"
  }));
}
var ForwardRef184 = React184.forwardRef(FlagIcon);
var FlagIcon_default = ForwardRef184;

// node_modules/@heroicons/react/20/solid/esm/FolderArrowDownIcon.js
var React185 = __toESM(require_react(), 1);
function FolderArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React185.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React185.createElement("title", {
    id: titleId
  }, title) : null, React185.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.75C2 3.784 2.784 3 3.75 3h4.836c.464 0 .909.184 1.237.513l1.414 1.414a.25.25 0 0 0 .177.073h4.836c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 16.25 17H3.75A1.75 1.75 0 0 1 2 15.25V4.75Zm8.75 4a.75.75 0 0 0-1.5 0v2.546l-.943-1.048a.75.75 0 1 0-1.114 1.004l2.25 2.5a.75.75 0 0 0 1.114 0l2.25-2.5a.75.75 0 1 0-1.114-1.004l-.943 1.048V8.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef185 = React185.forwardRef(FolderArrowDownIcon);
var FolderArrowDownIcon_default = ForwardRef185;

// node_modules/@heroicons/react/20/solid/esm/FolderMinusIcon.js
var React186 = __toESM(require_react(), 1);
function FolderMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React186.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React186.createElement("title", {
    id: titleId
  }, title) : null, React186.createElement("path", {
    fillRule: "evenodd",
    d: "M2 4.75C2 3.784 2.784 3 3.75 3h4.836c.464 0 .909.184 1.237.513l1.414 1.414a.25.25 0 0 0 .177.073h4.836c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 16.25 17H3.75A1.75 1.75 0 0 1 2 15.25V4.75Zm10.25 7a.75.75 0 0 0 0-1.5h-4.5a.75.75 0 0 0 0 1.5h4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef186 = React186.forwardRef(FolderMinusIcon);
var FolderMinusIcon_default = ForwardRef186;

// node_modules/@heroicons/react/20/solid/esm/FolderOpenIcon.js
var React187 = __toESM(require_react(), 1);
function FolderOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React187.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React187.createElement("title", {
    id: titleId
  }, title) : null, React187.createElement("path", {
    d: "M4.75 3A1.75 1.75 0 0 0 3 4.75v2.752l.104-.002h13.792c.035 0 .07 0 .104.002V6.75A1.75 1.75 0 0 0 15.25 5h-3.836a.25.25 0 0 1-.177-.073L9.823 3.513A1.75 1.75 0 0 0 8.586 3H4.75ZM3.104 9a1.75 1.75 0 0 0-1.673 2.265l1.385 4.5A1.75 1.75 0 0 0 4.488 17h11.023a1.75 1.75 0 0 0 1.673-1.235l1.384-4.5A1.75 1.75 0 0 0 16.896 9H3.104Z"
  }));
}
var ForwardRef187 = React187.forwardRef(FolderOpenIcon);
var FolderOpenIcon_default = ForwardRef187;

// node_modules/@heroicons/react/20/solid/esm/FolderPlusIcon.js
var React188 = __toESM(require_react(), 1);
function FolderPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React188.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React188.createElement("title", {
    id: titleId
  }, title) : null, React188.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 3A1.75 1.75 0 0 0 2 4.75v10.5c0 .966.784 1.75 1.75 1.75h12.5A1.75 1.75 0 0 0 18 15.25v-8.5A1.75 1.75 0 0 0 16.25 5h-4.836a.25.25 0 0 1-.177-.073L9.823 3.513A1.75 1.75 0 0 0 8.586 3H3.75ZM10 8a.75.75 0 0 1 .75.75v1.5h1.5a.75.75 0 0 1 0 1.5h-1.5v1.5a.75.75 0 0 1-1.5 0v-1.5h-1.5a.75.75 0 0 1 0-1.5h1.5v-1.5A.75.75 0 0 1 10 8Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef188 = React188.forwardRef(FolderPlusIcon);
var FolderPlusIcon_default = ForwardRef188;

// node_modules/@heroicons/react/20/solid/esm/FolderIcon.js
var React189 = __toESM(require_react(), 1);
function FolderIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React189.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React189.createElement("title", {
    id: titleId
  }, title) : null, React189.createElement("path", {
    d: "M3.75 3A1.75 1.75 0 0 0 2 4.75v3.26a3.235 3.235 0 0 1 1.75-.51h12.5c.644 0 1.245.188 1.75.51V6.75A1.75 1.75 0 0 0 16.25 5h-4.836a.25.25 0 0 1-.177-.073L9.823 3.513A1.75 1.75 0 0 0 8.586 3H3.75ZM3.75 9A1.75 1.75 0 0 0 2 10.75v4.5c0 .966.784 1.75 1.75 1.75h12.5A1.75 1.75 0 0 0 18 15.25v-4.5A1.75 1.75 0 0 0 16.25 9H3.75Z"
  }));
}
var ForwardRef189 = React189.forwardRef(FolderIcon);
var FolderIcon_default = ForwardRef189;

// node_modules/@heroicons/react/20/solid/esm/ForwardIcon.js
var React190 = __toESM(require_react(), 1);
function ForwardIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React190.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React190.createElement("title", {
    id: titleId
  }, title) : null, React190.createElement("path", {
    d: "M3.288 4.818A1.5 1.5 0 0 0 1 6.095v7.81a1.5 1.5 0 0 0 2.288 1.276l6.323-3.905c.155-.096.285-.213.389-.344v2.973a1.5 1.5 0 0 0 2.288 1.276l6.323-3.905a1.5 1.5 0 0 0 0-2.552l-6.323-3.906A1.5 1.5 0 0 0 10 6.095v2.972a1.506 1.506 0 0 0-.389-.343L3.288 4.818Z"
  }));
}
var ForwardRef190 = React190.forwardRef(ForwardIcon);
var ForwardIcon_default = ForwardRef190;

// node_modules/@heroicons/react/20/solid/esm/FunnelIcon.js
var React191 = __toESM(require_react(), 1);
function FunnelIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React191.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React191.createElement("title", {
    id: titleId
  }, title) : null, React191.createElement("path", {
    fillRule: "evenodd",
    d: "M2.628 1.601C5.028 1.206 7.49 1 10 1s4.973.206 7.372.601a.75.75 0 0 1 .628.74v2.288a2.25 2.25 0 0 1-.659 1.59l-4.682 4.683a2.25 2.25 0 0 0-.659 1.59v3.037c0 .684-.31 1.33-.844 1.757l-1.937 1.55A.75.75 0 0 1 8 18.25v-5.757a2.25 2.25 0 0 0-.659-1.591L2.659 6.22A2.25 2.25 0 0 1 2 4.629V2.34a.75.75 0 0 1 .628-.74Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef191 = React191.forwardRef(FunnelIcon);
var FunnelIcon_default = ForwardRef191;

// node_modules/@heroicons/react/20/solid/esm/GifIcon.js
var React192 = __toESM(require_react(), 1);
function GifIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React192.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React192.createElement("title", {
    id: titleId
  }, title) : null, React192.createElement("path", {
    fillRule: "evenodd",
    d: "M1 5.25A2.25 2.25 0 0 1 3.25 3h13.5A2.25 2.25 0 0 1 19 5.25v9.5A2.25 2.25 0 0 1 16.75 17H3.25A2.25 2.25 0 0 1 1 14.75v-9.5Zm4.026 2.879C5.356 7.65 5.72 7.5 6 7.5s.643.15.974.629a.75.75 0 0 0 1.234-.854C7.66 6.484 6.873 6 6 6c-.873 0-1.66.484-2.208 1.275C3.25 8.059 3 9.048 3 10c0 .952.25 1.941.792 2.725C4.34 13.516 5.127 14 6 14c.873 0 1.66-.484 2.208-1.275a.75.75 0 0 0 .133-.427V10a.75.75 0 0 0-.75-.75H6.25a.75.75 0 0 0 0 1.5h.591v1.295c-.293.342-.6.455-.841.455-.279 0-.643-.15-.974-.629C4.69 11.386 4.5 10.711 4.5 10c0-.711.19-1.386.526-1.871ZM10.75 6a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5a.75.75 0 0 1 .75-.75Zm3 0h2.5a.75.75 0 0 1 0 1.5H14.5v1.75h.75a.75.75 0 0 1 0 1.5h-.75v2.5a.75.75 0 0 1-1.5 0v-6.5a.75.75 0 0 1 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef192 = React192.forwardRef(GifIcon);
var GifIcon_default = ForwardRef192;

// node_modules/@heroicons/react/20/solid/esm/GiftTopIcon.js
var React193 = __toESM(require_react(), 1);
function GiftTopIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React193.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React193.createElement("title", {
    id: titleId
  }, title) : null, React193.createElement("path", {
    fillRule: "evenodd",
    d: "M9.25 3H3.5A1.5 1.5 0 0 0 2 4.5v4.75h3.365A2.75 2.75 0 0 1 9.25 5.362V3ZM2 10.75v4.75A1.5 1.5 0 0 0 3.5 17h5.75v-4.876A4.75 4.75 0 0 1 5 14.75a.75.75 0 0 1 0-1.5 3.251 3.251 0 0 0 3.163-2.5H2ZM10.75 17h5.75a1.5 1.5 0 0 0 1.5-1.5v-4.75h-6.163A3.251 3.251 0 0 0 15 13.25a.75.75 0 0 1 0 1.5 4.75 4.75 0 0 1-4.25-2.626V17ZM18 9.25V4.5A1.5 1.5 0 0 0 16.5 3h-5.75v2.362a2.75 2.75 0 0 1 3.885 3.888H18Zm-4.496-2.755a1.25 1.25 0 0 0-1.768 0c-.36.359-.526.999-.559 1.697-.01.228-.006.443.004.626.183.01.398.014.626.003.698-.033 1.338-.2 1.697-.559a1.25 1.25 0 0 0 0-1.767Zm-5.24 0a1.25 1.25 0 0 0-1.768 1.767c.36.36 1 .526 1.697.56.228.01.443.006.626-.004.01-.183.015-.398.004-.626-.033-.698-.2-1.338-.56-1.697Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef193 = React193.forwardRef(GiftTopIcon);
var GiftTopIcon_default = ForwardRef193;

// node_modules/@heroicons/react/20/solid/esm/GiftIcon.js
var React194 = __toESM(require_react(), 1);
function GiftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React194.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React194.createElement("title", {
    id: titleId
  }, title) : null, React194.createElement("path", {
    fillRule: "evenodd",
    d: "M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z",
    clipRule: "evenodd"
  }), React194.createElement("path", {
    d: "M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"
  }));
}
var ForwardRef194 = React194.forwardRef(GiftIcon);
var GiftIcon_default = ForwardRef194;

// node_modules/@heroicons/react/20/solid/esm/GlobeAltIcon.js
var React195 = __toESM(require_react(), 1);
function GlobeAltIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React195.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React195.createElement("title", {
    id: titleId
  }, title) : null, React195.createElement("path", {
    d: "M16.555 5.412a8.028 8.028 0 0 0-3.503-2.81 14.899 14.899 0 0 1 1.663 4.472 8.547 8.547 0 0 0 1.84-1.662ZM13.326 7.825a13.43 13.43 0 0 0-2.413-5.773 8.087 8.087 0 0 0-1.826 0 13.43 13.43 0 0 0-2.413 5.773A8.473 8.473 0 0 0 10 8.5c1.18 0 2.304-.24 3.326-.675ZM6.514 9.376A9.98 9.98 0 0 0 10 10c1.226 0 2.4-.22 3.486-.624a13.54 13.54 0 0 1-.351 3.759A13.54 13.54 0 0 1 10 13.5c-1.079 0-2.128-.127-3.134-.366a13.538 13.538 0 0 1-.352-3.758ZM5.285 7.074a14.9 14.9 0 0 1 1.663-4.471 8.028 8.028 0 0 0-3.503 2.81c.529.638 1.149 1.199 1.84 1.66ZM17.334 6.798a7.973 7.973 0 0 1 .614 4.115 13.47 13.47 0 0 1-3.178 1.72 15.093 15.093 0 0 0 .174-3.939 10.043 10.043 0 0 0 2.39-1.896ZM2.666 6.798a10.042 10.042 0 0 0 2.39 1.896 15.196 15.196 0 0 0 .174 3.94 13.472 13.472 0 0 1-3.178-1.72 7.973 7.973 0 0 1 .615-4.115ZM10 15c.898 0 1.778-.079 2.633-.23a13.473 13.473 0 0 1-1.72 3.178 8.099 8.099 0 0 1-1.826 0 13.47 13.47 0 0 1-1.72-3.178c.855.151 1.735.23 2.633.23ZM14.357 14.357a14.912 14.912 0 0 1-1.305 3.04 8.027 8.027 0 0 0 4.345-4.345c-.953.542-1.971.981-3.04 1.305ZM6.948 17.397a8.027 8.027 0 0 1-4.345-4.345c.953.542 1.971.981 3.04 1.305a14.912 14.912 0 0 0 1.305 3.04Z"
  }));
}
var ForwardRef195 = React195.forwardRef(GlobeAltIcon);
var GlobeAltIcon_default = ForwardRef195;

// node_modules/@heroicons/react/20/solid/esm/GlobeAmericasIcon.js
var React196 = __toESM(require_react(), 1);
function GlobeAmericasIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React196.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React196.createElement("title", {
    id: titleId
  }, title) : null, React196.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.5 0a6.5 6.5 0 1 1-11-4.69v.447a3.5 3.5 0 0 0 1.025 2.475L8.293 10 8 10.293a1 1 0 0 0 0 1.414l1.06 1.06a1.5 1.5 0 0 1 .44 1.061v.363a1 1 0 0 0 .553.894l.276.139a1 1 0 0 0 1.342-.448l1.454-2.908a1.5 1.5 0 0 0-.281-1.731l-.772-.772a1 1 0 0 0-1.023-.242l-.384.128a.5.5 0 0 1-.606-.25l-.296-.592a.481.481 0 0 1 .646-.646l.262.131a1 1 0 0 0 .447.106h.188a1 1 0 0 0 .949-1.316l-.068-.204a.5.5 0 0 1 .149-.538l1.44-1.234A6.492 6.492 0 0 1 16.5 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef196 = React196.forwardRef(GlobeAmericasIcon);
var GlobeAmericasIcon_default = ForwardRef196;

// node_modules/@heroicons/react/20/solid/esm/GlobeAsiaAustraliaIcon.js
var React197 = __toESM(require_react(), 1);
function GlobeAsiaAustraliaIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React197.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React197.createElement("title", {
    id: titleId
  }, title) : null, React197.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-6.5 6.326a6.52 6.52 0 0 1-1.5.174 6.487 6.487 0 0 1-5.011-2.36l.49-.98a.423.423 0 0 1 .614-.164l.294.196a.992.992 0 0 0 1.491-1.139l-.197-.593a.252.252 0 0 1 .126-.304l1.973-.987a.938.938 0 0 0 .361-1.359.375.375 0 0 1 .239-.576l.125-.025A2.421 2.421 0 0 0 12.327 6.6l.05-.149a1 1 0 0 0-.242-1.023l-1.489-1.489a.5.5 0 0 1-.146-.353v-.067a6.5 6.5 0 0 1 5.392 9.23 1.398 1.398 0 0 0-.68-.244l-.566-.566a1.5 1.5 0 0 0-1.06-.439h-.172a1.5 1.5 0 0 0-1.06.44l-.593.592a.501.501 0 0 1-.13.093l-1.578.79a1 1 0 0 0-.553.894v.191a1 1 0 0 0 1 1h.5a.5.5 0 0 1 .5.5v.326Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef197 = React197.forwardRef(GlobeAsiaAustraliaIcon);
var GlobeAsiaAustraliaIcon_default = ForwardRef197;

// node_modules/@heroicons/react/20/solid/esm/GlobeEuropeAfricaIcon.js
var React198 = __toESM(require_react(), 1);
function GlobeEuropeAfricaIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React198.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React198.createElement("title", {
    id: titleId
  }, title) : null, React198.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-1.503.204A6.5 6.5 0 1 1 7.95 3.83L6.927 5.62a1.453 1.453 0 0 0 1.91 2.02l.175-.087a.5.5 0 0 1 .224-.053h.146a.5.5 0 0 1 .447.724l-.028.055a.4.4 0 0 1-.357.221h-.502a2.26 2.26 0 0 0-1.88 1.006l-.044.066a2.099 2.099 0 0 0 1.085 *********** 0 0 1 .397.547v1.05a1.175 1.175 0 0 0 2.093.734l1.611-2.014c.192-.24.296-.536.296-.842 0-.316.128-.624.353-.85a1.363 1.363 0 0 0 .173-1.716l-.464-.696a.369.369 0 0 1 .527-.499l.343.257c.316.237.738.275 1.091.098a.586.586 0 0 1 .677.11l1.297 1.297Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef198 = React198.forwardRef(GlobeEuropeAfricaIcon);
var GlobeEuropeAfricaIcon_default = ForwardRef198;

// node_modules/@heroicons/react/20/solid/esm/H1Icon.js
var React199 = __toESM(require_react(), 1);
function H1Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React199.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React199.createElement("title", {
    id: titleId
  }, title) : null, React199.createElement("path", {
    fillRule: "evenodd",
    d: "M2.75 4a.75.75 0 0 1 .75.75v4.5h5v-4.5a.75.75 0 0 1 1.5 0v10.5a.75.75 0 0 1-1.5 0v-4.5h-5v4.5a.75.75 0 0 1-1.5 0V4.75A.75.75 0 0 1 2.75 4ZM13 8.75a.75.75 0 0 1 .75-.75h1.75a.75.75 0 0 1 .75.75v5.75h1a.75.75 0 0 1 0 1.5h-3.5a.75.75 0 0 1 0-1.5h1v-5h-1a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef199 = React199.forwardRef(H1Icon);
var H1Icon_default = ForwardRef199;

// node_modules/@heroicons/react/20/solid/esm/H2Icon.js
var React200 = __toESM(require_react(), 1);
function H2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React200.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React200.createElement("title", {
    id: titleId
  }, title) : null, React200.createElement("path", {
    fillRule: "evenodd",
    d: "M2.75 4a.75.75 0 0 1 .75.75v4.5h5v-4.5a.75.75 0 0 1 1.5 0v10.5a.75.75 0 0 1-1.5 0v-4.5h-5v4.5a.75.75 0 0 1-1.5 0V4.75A.75.75 0 0 1 2.75 4ZM15 9.5c-.729 0-1.445.051-2.146.15a.75.75 0 0 1-.208-1.486 16.887 16.887 0 0 1 3.824-.1c.855.074 1.512.78 1.527 1.637a17.476 17.476 0 0 1-.009.931 1.713 1.713 0 0 1-1.18 1.556l-2.453.818a1.25 1.25 0 0 0-.855 1.185v.309h3.75a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75v-1.059a2.75 2.75 0 0 1 1.88-2.608l2.454-.818c.102-.034.153-.117.155-.188a15.556 15.556 0 0 0 .009-.85.171.171 0 0 0-.158-.169A15.458 15.458 0 0 0 15 9.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef200 = React200.forwardRef(H2Icon);
var H2Icon_default = ForwardRef200;

// node_modules/@heroicons/react/20/solid/esm/H3Icon.js
var React201 = __toESM(require_react(), 1);
function H3Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React201.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React201.createElement("title", {
    id: titleId
  }, title) : null, React201.createElement("path", {
    fillRule: "evenodd",
    d: "M2.75 4a.75.75 0 0 1 .75.75v4.5h5v-4.5a.75.75 0 0 1 1.5 0v10.5a.75.75 0 0 1-1.5 0v-4.5h-5v4.5a.75.75 0 0 1-1.5 0V4.75A.75.75 0 0 1 2.75 4ZM15 9.5c-.73 0-1.448.051-2.15.15a.75.75 0 1 1-.209-1.485 16.886 16.886 0 0 1 3.476-.128c.985.065 1.878.837 1.883 1.932V10a6.75 6.75 0 0 1-.301 2A6.75 6.75 0 0 1 18 14v.031c-.005 1.095-.898 1.867-1.883 1.932a17.018 17.018 0 0 1-3.467-.127.75.75 0 0 1 .209-1.485 15.377 15.377 0 0 0 3.16.115c.308-.02.48-.24.48-.441L16.5 14c0-.431-.052-.85-.15-1.25h-2.6a.75.75 0 0 1 0-1.5h2.6c.098-.4.15-.818.15-1.25v-.024c-.001-.201-.173-.422-.481-.443A15.485 15.485 0 0 0 15 9.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef201 = React201.forwardRef(H3Icon);
var H3Icon_default = ForwardRef201;

// node_modules/@heroicons/react/20/solid/esm/HandRaisedIcon.js
var React202 = __toESM(require_react(), 1);
function HandRaisedIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React202.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React202.createElement("title", {
    id: titleId
  }, title) : null, React202.createElement("path", {
    fillRule: "evenodd",
    d: "M11 2a1 1 0 1 0-2 0v6.5a.5.5 0 0 1-1 0V3a1 1 0 1 0-2 0v5.5a.5.5 0 0 1-1 0V5a1 1 0 1 0-2 0v7a7 7 0 1 0 14 0V8a1 1 0 1 0-2 0v3.5a.5.5 0 0 1-1 0V3a1 1 0 1 0-2 0v5.5a.5.5 0 0 1-1 0V2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef202 = React202.forwardRef(HandRaisedIcon);
var HandRaisedIcon_default = ForwardRef202;

// node_modules/@heroicons/react/20/solid/esm/HandThumbDownIcon.js
var React203 = __toESM(require_react(), 1);
function HandThumbDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React203.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React203.createElement("title", {
    id: titleId
  }, title) : null, React203.createElement("path", {
    d: "M18.905 12.75a1.25 1.25 0 1 1-2.5 0v-7.5a1.25 1.25 0 0 1 2.5 0v7.5ZM8.905 17v1.3c0 .268-.14.526-.395.607A2 2 0 0 1 5.905 17c0-.995.182-1.948.514-2.826.204-.54-.166-1.174-.744-1.174h-2.52c-1.243 0-2.261-1.01-2.146-2.247.193-2.08.651-4.082 1.341-5.974C2.752 3.678 3.833 3 5.005 3h3.192a3 3 0 0 1 1.341.317l2.734 1.366A3 3 0 0 0 13.613 5h1.292v7h-.963c-.685 0-1.258.482-1.612 1.068a4.01 4.01 0 0 1-2.166 1.73c-.432.143-.853.386-1.011.814-.16.432-.248.9-.248 1.388Z"
  }));
}
var ForwardRef203 = React203.forwardRef(HandThumbDownIcon);
var HandThumbDownIcon_default = ForwardRef203;

// node_modules/@heroicons/react/20/solid/esm/HandThumbUpIcon.js
var React204 = __toESM(require_react(), 1);
function HandThumbUpIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React204.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React204.createElement("title", {
    id: titleId
  }, title) : null, React204.createElement("path", {
    d: "M1 8.25a1.25 1.25 0 1 1 2.5 0v7.5a1.25 1.25 0 1 1-2.5 0v-7.5ZM11 3V1.7c0-.268.14-.526.395-.607A2 2 0 0 1 14 3c0 .995-.182 1.948-.514 2.826-.204.54.166 1.174.744 1.174h2.52c1.243 0 2.261 1.01 2.146 2.247a23.864 23.864 0 0 1-1.341 5.974C17.153 16.323 16.072 17 14.9 17h-3.192a3 3 0 0 1-1.341-.317l-2.734-1.366A3 3 0 0 0 6.292 15H5V8h.963c.685 0 1.258-.483 1.612-1.068a4.011 4.011 0 0 1 2.166-1.73c.432-.143.853-.386 1.011-.814.16-.432.248-.9.248-1.388Z"
  }));
}
var ForwardRef204 = React204.forwardRef(HandThumbUpIcon);
var HandThumbUpIcon_default = ForwardRef204;

// node_modules/@heroicons/react/20/solid/esm/HashtagIcon.js
var React205 = __toESM(require_react(), 1);
function HashtagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React205.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React205.createElement("title", {
    id: titleId
  }, title) : null, React205.createElement("path", {
    fillRule: "evenodd",
    d: "M9.493 2.852a.75.75 0 0 0-1.486-.204L7.545 6H4.198a.75.75 0 0 0 0 1.5h3.14l-.69 5H3.302a.75.75 0 0 0 0 1.5h3.14l-.435 3.148a.75.75 0 0 0 1.486.204L7.955 14h2.986l-.434 3.148a.75.75 0 0 0 1.486.204L12.456 14h3.346a.75.75 0 0 0 0-1.5h-3.14l.69-5h3.346a.75.75 0 0 0 0-1.5h-3.14l.435-3.148a.75.75 0 0 0-1.486-.204L12.045 6H9.059l.434-3.148ZM8.852 7.5l-.69 5h2.986l.69-5H8.852Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef205 = React205.forwardRef(HashtagIcon);
var HashtagIcon_default = ForwardRef205;

// node_modules/@heroicons/react/20/solid/esm/HeartIcon.js
var React206 = __toESM(require_react(), 1);
function HeartIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React206.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React206.createElement("title", {
    id: titleId
  }, title) : null, React206.createElement("path", {
    d: "m9.653 16.915-.005-.003-.019-.01a20.759 20.759 0 0 1-1.162-.682 22.045 22.045 0 0 1-2.582-1.9C4.045 12.733 2 10.352 2 7.5a4.5 4.5 0 0 1 8-2.828A4.5 4.5 0 0 1 18 7.5c0 2.852-2.044 5.233-3.885 6.82a22.049 22.049 0 0 1-3.744 2.582l-.019.01-.005.003h-.002a.739.739 0 0 1-.69.001l-.002-.001Z"
  }));
}
var ForwardRef206 = React206.forwardRef(HeartIcon);
var HeartIcon_default = ForwardRef206;

// node_modules/@heroicons/react/20/solid/esm/HomeModernIcon.js
var React207 = __toESM(require_react(), 1);
function HomeModernIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React207.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React207.createElement("title", {
    id: titleId
  }, title) : null, React207.createElement("path", {
    d: "M14.916 2.404a.75.75 0 0 1-.32 1.011l-.596.31V17a1 1 0 0 1-1 1h-2.26a.75.75 0 0 1-.75-.75v-3.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.5a.75.75 0 0 1-.75.75h-3.5a.75.75 0 0 1 0-1.5H2V9.957a.75.75 0 0 1-.596-1.372L2 8.275V5.75a.75.75 0 0 1 1.5 0v1.745l10.404-5.41a.75.75 0 0 1 1.012.319ZM15.861 8.57a.75.75 0 0 1 .736-.025l1.999 1.04A.75.75 0 0 1 18 10.957V16.5h.25a.75.75 0 0 1 0 1.5h-2a.75.75 0 0 1-.75-.75V9.21a.75.75 0 0 1 .361-.64Z"
  }));
}
var ForwardRef207 = React207.forwardRef(HomeModernIcon);
var HomeModernIcon_default = ForwardRef207;

// node_modules/@heroicons/react/20/solid/esm/HomeIcon.js
var React208 = __toESM(require_react(), 1);
function HomeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React208.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React208.createElement("title", {
    id: titleId
  }, title) : null, React208.createElement("path", {
    fillRule: "evenodd",
    d: "M9.293 2.293a1 1 0 0 1 1.414 0l7 7A1 1 0 0 1 17 11h-1v6a1 1 0 0 1-1 1h-2a1 1 0 0 1-1-1v-3a1 1 0 0 0-1-1H9a1 1 0 0 0-1 1v3a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-6H3a1 1 0 0 1-.707-1.707l7-7Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef208 = React208.forwardRef(HomeIcon);
var HomeIcon_default = ForwardRef208;

// node_modules/@heroicons/react/20/solid/esm/IdentificationIcon.js
var React209 = __toESM(require_react(), 1);
function IdentificationIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React209.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React209.createElement("title", {
    id: titleId
  }, title) : null, React209.createElement("path", {
    fillRule: "evenodd",
    d: "M1 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v8a3 3 0 0 1-3 3H4a3 3 0 0 1-3-3V6Zm4 1.5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm2 3a4 4 0 0 0-3.665 2.395.75.75 0 0 0 .416 1A8.98 8.98 0 0 0 7 14.5a8.98 8.98 0 0 0 3.249-.604.75.75 0 0 0 .416-1.001A4.001 4.001 0 0 0 7 10.5Zm5-3.75a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Zm0 6.5a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Zm.75-4a.75.75 0 0 0 0 1.5h2.5a.75.75 0 0 0 0-1.5h-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef209 = React209.forwardRef(IdentificationIcon);
var IdentificationIcon_default = ForwardRef209;

// node_modules/@heroicons/react/20/solid/esm/InboxArrowDownIcon.js
var React210 = __toESM(require_react(), 1);
function InboxArrowDownIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React210.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React210.createElement("title", {
    id: titleId
  }, title) : null, React210.createElement("path", {
    d: "M10 2a.75.75 0 0 1 .75.75v5.59l1.95-2.1a.75.75 0 1 1 1.1 1.02l-3.25 3.5a.75.75 0 0 1-1.1 0L6.2 7.26a.75.75 0 1 1 1.1-1.02l1.95 2.1V2.75A.75.75 0 0 1 10 2Z"
  }), React210.createElement("path", {
    d: "M5.273 4.5a1.25 1.25 0 0 0-1.205.918l-1.523 5.52c-.006.02-.01.041-.015.062H6a1 1 0 0 1 .894.553l.448.894a1 1 0 0 0 .894.553h3.438a1 1 0 0 0 .86-.49l.606-1.02A1 1 0 0 1 14 11h3.47a1.318 1.318 0 0 0-.015-.062l-1.523-5.52a1.25 1.25 0 0 0-1.205-.918h-.977a.75.75 0 0 1 0-1.5h.977a2.75 2.75 0 0 1 2.651 2.019l1.523 5.52c.066.239.099.485.099.732V15a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-3.73c0-.246.033-.492.099-.73l1.523-5.521A2.75 2.75 0 0 1 5.273 3h.977a.75.75 0 0 1 0 1.5h-.977Z"
  }));
}
var ForwardRef210 = React210.forwardRef(InboxArrowDownIcon);
var InboxArrowDownIcon_default = ForwardRef210;

// node_modules/@heroicons/react/20/solid/esm/InboxStackIcon.js
var React211 = __toESM(require_react(), 1);
function InboxStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React211.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React211.createElement("title", {
    id: titleId
  }, title) : null, React211.createElement("path", {
    fillRule: "evenodd",
    d: "M1.045 6.954a2.75 2.75 0 0 1 .217-.678L2.53 3.58A2.75 2.75 0 0 1 5.019 2h9.962a2.75 2.75 0 0 1 2.488 1.58l1.27 2.696c.101.216.174.444.216.678A1 1 0 0 1 19 7.25v1.5a2.75 2.75 0 0 1-2.75 2.75H3.75A2.75 2.75 0 0 1 1 8.75v-1.5a1 1 0 0 1 .045-.296Zm2.843-2.736A1.25 1.25 0 0 1 5.02 3.5h9.962c.484 0 .925.28 1.13.718l.957 2.032H14a1 1 0 0 0-.86.49l-.606 1.02a1 1 0 0 1-.86.49H8.236a1 1 0 0 1-.894-.553l-.448-.894A1 1 0 0 0 6 6.25H2.932l.956-2.032Z",
    clipRule: "evenodd"
  }), React211.createElement("path", {
    d: "M1 14a1 1 0 0 1 1-1h4a1 1 0 0 1 .894.553l.448.894a1 1 0 0 0 .894.553h3.438a1 1 0 0 0 .86-.49l.606-1.02A1 1 0 0 1 14 13h4a1 1 0 0 1 1 1v2a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-2Z"
  }));
}
var ForwardRef211 = React211.forwardRef(InboxStackIcon);
var InboxStackIcon_default = ForwardRef211;

// node_modules/@heroicons/react/20/solid/esm/InboxIcon.js
var React212 = __toESM(require_react(), 1);
function InboxIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React212.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React212.createElement("title", {
    id: titleId
  }, title) : null, React212.createElement("path", {
    fillRule: "evenodd",
    d: "M1 11.27c0-.246.033-.492.099-.73l1.523-5.521A2.75 2.75 0 0 1 5.273 3h9.454a2.75 2.75 0 0 1 2.651 2.019l1.523 5.52c.066.239.099.485.099.732V15a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2v-3.73Zm3.068-5.852A1.25 1.25 0 0 1 5.273 4.5h9.454a1.25 1.25 0 0 1 1.205.918l1.523 5.52c.*************.015.062H14a1 1 0 0 0-.86.49l-.606 1.02a1 1 0 0 1-.86.49H8.236a1 1 0 0 1-.894-.553l-.448-.894A1 1 0 0 0 6 11H2.53l.015-.062 1.523-5.52Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef212 = React212.forwardRef(InboxIcon);
var InboxIcon_default = ForwardRef212;

// node_modules/@heroicons/react/20/solid/esm/InformationCircleIcon.js
var React213 = __toESM(require_react(), 1);
function InformationCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React213.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React213.createElement("title", {
    id: titleId
  }, title) : null, React213.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-7-4a1 1 0 1 1-2 0 1 1 0 0 1 2 0ZM9 9a.75.75 0 0 0 0 1.5h.253a.25.25 0 0 1 .244.304l-.459 2.066A1.75 1.75 0 0 0 10.747 15H11a.75.75 0 0 0 0-1.5h-.253a.25.25 0 0 1-.244-.304l.459-2.066A1.75 1.75 0 0 0 9.253 9H9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef213 = React213.forwardRef(InformationCircleIcon);
var InformationCircleIcon_default = ForwardRef213;

// node_modules/@heroicons/react/20/solid/esm/ItalicIcon.js
var React214 = __toESM(require_react(), 1);
function ItalicIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React214.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React214.createElement("title", {
    id: titleId
  }, title) : null, React214.createElement("path", {
    fillRule: "evenodd",
    d: "M8 2.75A.75.75 0 0 1 8.75 2h7.5a.75.75 0 0 1 0 1.5h-3.215l-4.483 13h2.698a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3.215l4.483-13H8.75A.75.75 0 0 1 8 2.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef214 = React214.forwardRef(ItalicIcon);
var ItalicIcon_default = ForwardRef214;

// node_modules/@heroicons/react/20/solid/esm/KeyIcon.js
var React215 = __toESM(require_react(), 1);
function KeyIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React215.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React215.createElement("title", {
    id: titleId
  }, title) : null, React215.createElement("path", {
    fillRule: "evenodd",
    d: "M8 7a5 5 0 1 1 3.61 4.804l-1.903 1.903A1 1 0 0 1 9 14H8v1a1 1 0 0 1-1 1H6v1a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1v-2a1 1 0 0 1 .293-.707L8.196 8.39A5.002 5.002 0 0 1 8 7Zm5-3a.75.75 0 0 0 0 1.5A1.5 1.5 0 0 1 14.5 7 .75.75 0 0 0 16 7a3 3 0 0 0-3-3Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef215 = React215.forwardRef(KeyIcon);
var KeyIcon_default = ForwardRef215;

// node_modules/@heroicons/react/20/solid/esm/LanguageIcon.js
var React216 = __toESM(require_react(), 1);
function LanguageIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React216.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React216.createElement("title", {
    id: titleId
  }, title) : null, React216.createElement("path", {
    d: "M7.75 2.75a.75.75 0 0 0-1.5 0v1.258a32.987 32.987 0 0 0-3.599.278.75.75 0 1 0 .198 1.487A31.545 31.545 0 0 1 8.7 5.545 19.381 19.381 0 0 1 7 9.56a19.418 19.418 0 0 1-1.002-********** 0 0 0-1.384.577 20.935 20.935 0 0 0 1.492 2.91 19.613 19.613 0 0 1-3.828 *********** 0 1 0 .945 1.164A21.116 21.116 0 0 0 7 12.331c.095.132.192.262.29.391a.75.75 0 0 0 1.194-.91c-.204-.266-.4-.538-.59-.815a20.888 20.888 0 0 0 2.333-5.332c.31.031.618.068.924.108a.75.75 0 0 0 .198-1.487 32.832 32.832 0 0 0-3.599-.278V2.75Z"
  }), React216.createElement("path", {
    fillRule: "evenodd",
    d: "M13 8a.75.75 0 0 1 .671.415l4.25 8.5a.75.75 0 1 1-1.342.67L15.787 16h-5.573l-.793 1.585a.75.75 0 1 1-1.342-.67l4.25-8.5A.75.75 0 0 1 13 8Zm2.037 6.5L13 10.427 10.964 14.5h4.073Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef216 = React216.forwardRef(LanguageIcon);
var LanguageIcon_default = ForwardRef216;

// node_modules/@heroicons/react/20/solid/esm/LifebuoyIcon.js
var React217 = __toESM(require_react(), 1);
function LifebuoyIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React217.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React217.createElement("title", {
    id: titleId
  }, title) : null, React217.createElement("path", {
    fillRule: "evenodd",
    d: "m7.171 4.146 1.947 2.466a3.514 3.514 0 0 1 1.764 0l1.947-2.466a6.52 6.52 0 0 0-5.658 0Zm8.683 3.025-2.466 1.947c.15.578.15 1.186 0 1.764l2.466 1.947a6.52 6.52 0 0 0 0-5.658Zm-3.025 8.683-1.947-2.466c-.578.15-1.186.15-1.764 0l-1.947 2.466a6.52 6.52 0 0 0 5.658 0ZM4.146 12.83l2.466-1.947a3.514 3.514 0 0 1 0-1.764L4.146 7.171a6.52 6.52 0 0 0 0 5.658ZM5.63 3.297a8.01 8.01 0 0 1 8.738 0 8.031 8.031 0 0 1 2.334 2.334 8.01 8.01 0 0 1 0 8.738 8.033 8.033 0 0 1-2.334 2.334 8.01 8.01 0 0 1-8.738 0 8.032 8.032 0 0 1-2.334-2.334 8.01 8.01 0 0 1 0-8.738A8.03 8.03 0 0 1 5.63 3.297Zm5.198 4.882a2.008 2.008 0 0 0-2.243.407 1.994 1.994 0 0 0-.407 2.243 1.993 1.993 0 0 0 .992.992 2.008 2.008 0 0 0 2.243-.407c.176-.175.31-.374.407-.585a2.008 2.008 0 0 0-.407-2.243 1.993 1.993 0 0 0-.585-.407Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef217 = React217.forwardRef(LifebuoyIcon);
var LifebuoyIcon_default = ForwardRef217;

// node_modules/@heroicons/react/20/solid/esm/LightBulbIcon.js
var React218 = __toESM(require_react(), 1);
function LightBulbIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React218.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React218.createElement("title", {
    id: titleId
  }, title) : null, React218.createElement("path", {
    d: "M10 1a6 6 0 0 0-3.815 10.631C7.237 12.5 8 13.443 8 14.456v.644a.75.75 0 0 0 .572.729 6.016 6.016 0 0 0 2.856 0A.75.75 0 0 0 12 15.1v-.644c0-1.013.762-1.957 1.815-2.825A6 6 0 0 0 10 1ZM8.863 17.414a.75.75 0 0 0-.226 1.483 9.066 9.066 0 0 0 2.726 0 .75.75 0 0 0-.226-1.483 7.553 7.553 0 0 1-2.274 0Z"
  }));
}
var ForwardRef218 = React218.forwardRef(LightBulbIcon);
var LightBulbIcon_default = ForwardRef218;

// node_modules/@heroicons/react/20/solid/esm/LinkSlashIcon.js
var React219 = __toESM(require_react(), 1);
function LinkSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React219.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React219.createElement("title", {
    id: titleId
  }, title) : null, React219.createElement("path", {
    fillRule: "evenodd",
    d: "M2.22 2.22a.75.75 0 0 1 1.06 0l4.46 4.46c.128-.178.272-.349.432-.508l3-3a4 4 0 0 1 5.657 5.656l-1.225 1.225a.75.75 0 1 1-1.06-1.06l1.224-1.225a2.5 2.5 0 0 0-3.536-3.536l-3 3a2.504 2.504 0 0 0-.406.533l2.59 2.59a2.49 2.49 0 0 0-.79-*********** 0 1 1 .977-1.138 3.997 3.997 0 0 1 1.306 3.886l4.871 4.87a.75.75 0 1 1-1.06 1.061l-5.177-5.177-.006-.005-4.134-4.134a.65.65 0 0 1-.005-.006L2.22 3.28a.75.75 0 0 1 0-1.06Zm3.237 7.727a.75.75 0 0 1 0 1.06l-1.225 1.225a2.5 2.5 0 0 0 3.536 3.536l1.879-1.879a.75.75 0 1 1 1.06 1.06L8.83 16.83a4 4 0 0 1-5.657-5.657l1.224-1.225a.75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef219 = React219.forwardRef(LinkSlashIcon);
var LinkSlashIcon_default = ForwardRef219;

// node_modules/@heroicons/react/20/solid/esm/LinkIcon.js
var React220 = __toESM(require_react(), 1);
function LinkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React220.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React220.createElement("title", {
    id: titleId
  }, title) : null, React220.createElement("path", {
    d: "M12.232 4.232a2.5 2.5 0 0 1 3.536 3.536l-1.225 1.224a.75.75 0 0 0 1.061 1.06l1.224-1.224a4 4 0 0 0-5.656-5.656l-3 3a4 4 0 0 0 .225 5.865.75.75 0 0 0 .977-1.138 2.5 2.5 0 0 1-.142-3.667l3-3Z"
  }), React220.createElement("path", {
    d: "M11.603 7.963a.75.75 0 0 0-.977 1.138 2.5 2.5 0 0 1 .142 3.667l-3 3a2.5 2.5 0 0 1-3.536-3.536l1.225-1.224a.75.75 0 0 0-1.061-1.06l-1.224 1.224a4 4 0 1 0 5.656 5.656l3-3a4 4 0 0 0-.225-5.865Z"
  }));
}
var ForwardRef220 = React220.forwardRef(LinkIcon);
var LinkIcon_default = ForwardRef220;

// node_modules/@heroicons/react/20/solid/esm/ListBulletIcon.js
var React221 = __toESM(require_react(), 1);
function ListBulletIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React221.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React221.createElement("title", {
    id: titleId
  }, title) : null, React221.createElement("path", {
    fillRule: "evenodd",
    d: "M6 4.75A.75.75 0 0 1 6.75 4h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 4.75ZM6 10a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 10Zm0 5.25a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H6.75a.75.75 0 0 1-.75-.75ZM1.99 4.75a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 15.25a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1v-.01ZM1.99 10a1 1 0 0 1 1-1H3a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1V10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef221 = React221.forwardRef(ListBulletIcon);
var ListBulletIcon_default = ForwardRef221;

// node_modules/@heroicons/react/20/solid/esm/LockClosedIcon.js
var React222 = __toESM(require_react(), 1);
function LockClosedIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React222.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React222.createElement("title", {
    id: titleId
  }, title) : null, React222.createElement("path", {
    fillRule: "evenodd",
    d: "M10 1a4.5 4.5 0 0 0-4.5 4.5V9H5a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2h-.5V5.5A4.5 4.5 0 0 0 10 1Zm3 8V5.5a3 3 0 1 0-6 0V9h6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef222 = React222.forwardRef(LockClosedIcon);
var LockClosedIcon_default = ForwardRef222;

// node_modules/@heroicons/react/20/solid/esm/LockOpenIcon.js
var React223 = __toESM(require_react(), 1);
function LockOpenIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React223.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React223.createElement("title", {
    id: titleId
  }, title) : null, React223.createElement("path", {
    fillRule: "evenodd",
    d: "M14.5 1A4.5 4.5 0 0 0 10 5.5V9H3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-6a2 2 0 0 0-2-2h-1.5V5.5a3 3 0 1 1 6 0v2.75a.75.75 0 0 0 1.5 0V5.5A4.5 4.5 0 0 0 14.5 1Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef223 = React223.forwardRef(LockOpenIcon);
var LockOpenIcon_default = ForwardRef223;

// node_modules/@heroicons/react/20/solid/esm/MagnifyingGlassCircleIcon.js
var React224 = __toESM(require_react(), 1);
function MagnifyingGlassCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React224.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React224.createElement("title", {
    id: titleId
  }, title) : null, React224.createElement("path", {
    d: "M6.5 9a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0Z"
  }), React224.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM9 5a4 4 0 1 0 2.248 7.309l1.472 1.471a.75.75 0 1 0 1.06-1.06l-1.471-1.472A4 4 0 0 0 9 5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef224 = React224.forwardRef(MagnifyingGlassCircleIcon);
var MagnifyingGlassCircleIcon_default = ForwardRef224;

// node_modules/@heroicons/react/20/solid/esm/MagnifyingGlassMinusIcon.js
var React225 = __toESM(require_react(), 1);
function MagnifyingGlassMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React225.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React225.createElement("title", {
    id: titleId
  }, title) : null, React225.createElement("path", {
    d: "M6.75 8.25a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-4.5Z"
  }), React225.createElement("path", {
    fillRule: "evenodd",
    d: "M9 2a7 7 0 1 0 4.391 12.452l3.329 3.328a.75.75 0 1 0 1.06-1.06l-3.328-3.329A7 7 0 0 0 9 2ZM3.5 9a5.5 5.5 0 1 1 11 0 5.5 5.5 0 0 1-11 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef225 = React225.forwardRef(MagnifyingGlassMinusIcon);
var MagnifyingGlassMinusIcon_default = ForwardRef225;

// node_modules/@heroicons/react/20/solid/esm/MagnifyingGlassPlusIcon.js
var React226 = __toESM(require_react(), 1);
function MagnifyingGlassPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React226.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React226.createElement("title", {
    id: titleId
  }, title) : null, React226.createElement("path", {
    d: "M9 6a.75.75 0 0 1 .75.75v1.5h1.5a.75.75 0 0 1 0 1.5h-1.5v1.5a.75.75 0 0 1-1.5 0v-1.5h-1.5a.75.75 0 0 1 0-1.5h1.5v-1.5A.75.75 0 0 1 9 6Z"
  }), React226.createElement("path", {
    fillRule: "evenodd",
    d: "M2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Zm7-5.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef226 = React226.forwardRef(MagnifyingGlassPlusIcon);
var MagnifyingGlassPlusIcon_default = ForwardRef226;

// node_modules/@heroicons/react/20/solid/esm/MagnifyingGlassIcon.js
var React227 = __toESM(require_react(), 1);
function MagnifyingGlassIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React227.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React227.createElement("title", {
    id: titleId
  }, title) : null, React227.createElement("path", {
    fillRule: "evenodd",
    d: "M9 3.5a5.5 5.5 0 1 0 0 11 5.5 5.5 0 0 0 0-11ZM2 9a7 7 0 1 1 12.452 4.391l3.328 3.329a.75.75 0 1 1-1.06 1.06l-3.329-3.328A7 7 0 0 1 2 9Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef227 = React227.forwardRef(MagnifyingGlassIcon);
var MagnifyingGlassIcon_default = ForwardRef227;

// node_modules/@heroicons/react/20/solid/esm/MapPinIcon.js
var React228 = __toESM(require_react(), 1);
function MapPinIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React228.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React228.createElement("title", {
    id: titleId
  }, title) : null, React228.createElement("path", {
    fillRule: "evenodd",
    d: "m9.69 18.933.003.001C9.89 19.02 10 19 10 19s.11.02.308-.066l.002-.001.006-.003.018-.008a5.741 5.741 0 0 0 .281-.14c.186-.096.446-.24.757-.433.62-.384 1.445-.966 2.274-1.765C15.302 14.988 17 12.493 17 9A7 7 0 1 0 3 9c0 3.492 1.698 5.988 3.355 7.584a13.731 13.731 0 0 0 2.273 1.765 11.842 11.842 0 0 0 .976.544l.***************.006.003ZM10 11.25a2.25 2.25 0 1 0 0-4.5 2.25 2.25 0 0 0 0 4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef228 = React228.forwardRef(MapPinIcon);
var MapPinIcon_default = ForwardRef228;

// node_modules/@heroicons/react/20/solid/esm/MapIcon.js
var React229 = __toESM(require_react(), 1);
function MapIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React229.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React229.createElement("title", {
    id: titleId
  }, title) : null, React229.createElement("path", {
    fillRule: "evenodd",
    d: "M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef229 = React229.forwardRef(MapIcon);
var MapIcon_default = ForwardRef229;

// node_modules/@heroicons/react/20/solid/esm/MegaphoneIcon.js
var React230 = __toESM(require_react(), 1);
function MegaphoneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React230.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React230.createElement("title", {
    id: titleId
  }, title) : null, React230.createElement("path", {
    d: "M13.92 3.845a19.362 19.362 0 0 1-6.3 1.98C6.765 5.942 5.89 6 5 6a4 4 0 0 0-.504 7.969 15.97 15.97 0 0 0 1.271 3.34c.397.771 1.342 1 2.05.59l.867-.5c.726-.419.94-1.32.588-2.02-.166-.331-.315-.666-.448-1.004 1.8.357 3.511.963 5.096 1.78A17.964 17.964 0 0 0 15 10c0-2.162-.381-4.235-1.08-6.155ZM15.243 3.097A19.456 19.456 0 0 1 16.5 10c0 2.43-.445 4.758-1.257 6.904l-.03.077a.75.75 0 0 0 1.401.537 20.903 20.903 0 0 0 1.312-5.745 2 2 0 0 0 0-3.546 20.902 20.902 0 0 0-1.312-5.745.75.75 0 0 0-1.4.537l.029.078Z"
  }));
}
var ForwardRef230 = React230.forwardRef(MegaphoneIcon);
var MegaphoneIcon_default = ForwardRef230;

// node_modules/@heroicons/react/20/solid/esm/MicrophoneIcon.js
var React231 = __toESM(require_react(), 1);
function MicrophoneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React231.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React231.createElement("title", {
    id: titleId
  }, title) : null, React231.createElement("path", {
    d: "M7 4a3 3 0 0 1 6 0v6a3 3 0 1 1-6 0V4Z"
  }), React231.createElement("path", {
    d: "M5.5 9.643a.75.75 0 0 0-1.5 0V10c0 3.06 2.29 5.585 5.25 5.954V17.5h-1.5a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-1.5v-1.546A6.001 6.001 0 0 0 16 10v-.357a.75.75 0 0 0-1.5 0V10a4.5 4.5 0 0 1-9 0v-.357Z"
  }));
}
var ForwardRef231 = React231.forwardRef(MicrophoneIcon);
var MicrophoneIcon_default = ForwardRef231;

// node_modules/@heroicons/react/20/solid/esm/MinusCircleIcon.js
var React232 = __toESM(require_react(), 1);
function MinusCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React232.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React232.createElement("title", {
    id: titleId
  }, title) : null, React232.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM6.75 9.25a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef232 = React232.forwardRef(MinusCircleIcon);
var MinusCircleIcon_default = ForwardRef232;

// node_modules/@heroicons/react/20/solid/esm/MinusSmallIcon.js
var React233 = __toESM(require_react(), 1);
function MinusSmallIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React233.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React233.createElement("title", {
    id: titleId
  }, title) : null, React233.createElement("path", {
    d: "M6.75 9.25a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"
  }));
}
var ForwardRef233 = React233.forwardRef(MinusSmallIcon);
var MinusSmallIcon_default = ForwardRef233;

// node_modules/@heroicons/react/20/solid/esm/MinusIcon.js
var React234 = __toESM(require_react(), 1);
function MinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React234.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React234.createElement("title", {
    id: titleId
  }, title) : null, React234.createElement("path", {
    fillRule: "evenodd",
    d: "M4 10a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H4.75A.75.75 0 0 1 4 10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef234 = React234.forwardRef(MinusIcon);
var MinusIcon_default = ForwardRef234;

// node_modules/@heroicons/react/20/solid/esm/MoonIcon.js
var React235 = __toESM(require_react(), 1);
function MoonIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React235.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React235.createElement("title", {
    id: titleId
  }, title) : null, React235.createElement("path", {
    fillRule: "evenodd",
    d: "M7.455 2.004a.75.75 0 0 1 .26.77 7 7 0 0 0 9.958 7.967.75.75 0 0 1 1.067.853A8.5 8.5 0 1 1 6.647 1.921a.75.75 0 0 1 .808.083Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef235 = React235.forwardRef(MoonIcon);
var MoonIcon_default = ForwardRef235;

// node_modules/@heroicons/react/20/solid/esm/MusicalNoteIcon.js
var React236 = __toESM(require_react(), 1);
function MusicalNoteIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React236.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React236.createElement("title", {
    id: titleId
  }, title) : null, React236.createElement("path", {
    fillRule: "evenodd",
    d: "M17.721 1.599a.75.75 0 0 1 .279.583v11.29a2.25 2.25 0 0 1-1.774 2.2l-2.041.44a2.216 2.216 0 0 1-.938-4.332l2.662-.577a.75.75 0 0 0 .591-.733V6.112l-8 1.73v7.684a2.25 2.25 0 0 1-1.774 2.2l-2.042.44a2.216 2.216 0 1 1-.935-4.331l2.659-.573A.75.75 0 0 0 7 12.529V4.236a.75.75 0 0 1 .591-.733l9.5-2.054a.75.75 0 0 1 .63.15Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef236 = React236.forwardRef(MusicalNoteIcon);
var MusicalNoteIcon_default = ForwardRef236;

// node_modules/@heroicons/react/20/solid/esm/NewspaperIcon.js
var React237 = __toESM(require_react(), 1);
function NewspaperIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React237.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React237.createElement("title", {
    id: titleId
  }, title) : null, React237.createElement("path", {
    fillRule: "evenodd",
    d: "M2 3.5A1.5 1.5 0 0 1 3.5 2h9A1.5 1.5 0 0 1 14 3.5v11.75A2.75 2.75 0 0 0 16.75 18h-12A2.75 2.75 0 0 1 2 15.25V3.5Zm3.75 7a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-4.5Zm0 3a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-4.5ZM5 5.75A.75.75 0 0 1 5.75 5h4.5a.75.75 0 0 1 .75.75v2.5a.75.75 0 0 1-.75.75h-4.5A.75.75 0 0 1 5 8.25v-2.5Z",
    clipRule: "evenodd"
  }), React237.createElement("path", {
    d: "M16.5 6.5h-1v8.75a1.25 1.25 0 1 0 2.5 0V8a1.5 1.5 0 0 0-1.5-1.5Z"
  }));
}
var ForwardRef237 = React237.forwardRef(NewspaperIcon);
var NewspaperIcon_default = ForwardRef237;

// node_modules/@heroicons/react/20/solid/esm/NoSymbolIcon.js
var React238 = __toESM(require_react(), 1);
function NoSymbolIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React238.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React238.createElement("title", {
    id: titleId
  }, title) : null, React238.createElement("path", {
    fillRule: "evenodd",
    d: "m5.965 4.904 9.131 9.131a6.5 6.5 0 0 0-9.131-9.131Zm8.07 10.192L4.904 5.965a6.5 6.5 0 0 0 9.131 9.131ZM4.343 4.343a8 8 0 1 1 11.314 11.314A8 8 0 0 1 4.343 4.343Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef238 = React238.forwardRef(NoSymbolIcon);
var NoSymbolIcon_default = ForwardRef238;

// node_modules/@heroicons/react/20/solid/esm/NumberedListIcon.js
var React239 = __toESM(require_react(), 1);
function NumberedListIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React239.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React239.createElement("title", {
    id: titleId
  }, title) : null, React239.createElement("path", {
    d: "M3 1.25a.75.75 0 0 0 0 1.5h.25v2.5a.75.75 0 0 0 1.5 0V2A.75.75 0 0 0 4 1.25H3ZM2.97 8.654a3.5 3.5 0 0 1 1.524-.12.034.034 0 0 1-.012.012L2.415 9.579A.75.75 0 0 0 2 10.25v1c0 .414.336.75.75.75h2.5a.75.75 0 0 0 0-1.5H3.927l1.225-.613c.52-.26.848-.79.848-1.371 0-.647-.429-1.327-1.193-1.451a5.03 5.03 0 0 0-2.27*********** 0 0 0 .44 1.434ZM7.75 3a.75.75 0 0 0 0 1.5h9.5a.75.75 0 0 0 0-1.5h-9.5ZM7.75 9.25a.75.75 0 0 0 0 1.5h9.5a.75.75 0 0 0 0-1.5h-9.5ZM7.75 15.5a.75.75 0 0 0 0 1.5h9.5a.75.75 0 0 0 0-1.5h-9.5ZM2.625 13.875a.75.75 0 0 0 0 1.5h1.5a.125.125 0 0 1 0 .25H3.5a.75.75 0 0 0 0 1.5h.625a.125.125 0 0 1 0 .25h-1.5a.75.75 0 0 0 0 1.5h1.5a1.625 1.625 0 0 0 1.37-2.5 1.625 1.625 0 0 0-1.37-2.5h-1.5Z"
  }));
}
var ForwardRef239 = React239.forwardRef(NumberedListIcon);
var NumberedListIcon_default = ForwardRef239;

// node_modules/@heroicons/react/20/solid/esm/PaintBrushIcon.js
var React240 = __toESM(require_react(), 1);
function PaintBrushIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React240.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React240.createElement("title", {
    id: titleId
  }, title) : null, React240.createElement("path", {
    d: "M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"
  }));
}
var ForwardRef240 = React240.forwardRef(PaintBrushIcon);
var PaintBrushIcon_default = ForwardRef240;

// node_modules/@heroicons/react/20/solid/esm/PaperAirplaneIcon.js
var React241 = __toESM(require_react(), 1);
function PaperAirplaneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React241.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React241.createElement("title", {
    id: titleId
  }, title) : null, React241.createElement("path", {
    d: "M3.105 2.288a.75.75 0 0 0-.826.95l1.414 4.926A1.5 1.5 0 0 0 5.135 9.25h6.115a.75.75 0 0 1 0 1.5H5.135a1.5 1.5 0 0 0-1.442 1.086l-1.414 4.926a.75.75 0 0 0 .826.95 28.897 28.897 0 0 0 15.293-*********** 0 0 0 0-1.114A28.897 28.897 0 0 0 3.105 2.288Z"
  }));
}
var ForwardRef241 = React241.forwardRef(PaperAirplaneIcon);
var PaperAirplaneIcon_default = ForwardRef241;

// node_modules/@heroicons/react/20/solid/esm/PaperClipIcon.js
var React242 = __toESM(require_react(), 1);
function PaperClipIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React242.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React242.createElement("title", {
    id: titleId
  }, title) : null, React242.createElement("path", {
    fillRule: "evenodd",
    d: "M15.621 4.379a3 3 0 0 0-4.242 0l-7 7a3 3 0 0 0 4.241 4.243h.001l.497-.5a.75.75 0 0 1 1.064 1.057l-.498.501-.002.002a4.5 4.5 0 0 1-6.364-6.364l7-7a4.5 4.5 0 0 1 6.368 6.36l-3.455 3.553A2.625 2.625 0 1 1 9.52 9.52l3.45-3.451a.75.75 0 1 1 1.061 1.06l-3.45 3.451a1.125 1.125 0 0 0 1.587 1.595l3.454-3.553a3 3 0 0 0 0-4.242Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef242 = React242.forwardRef(PaperClipIcon);
var PaperClipIcon_default = ForwardRef242;

// node_modules/@heroicons/react/20/solid/esm/PauseCircleIcon.js
var React243 = __toESM(require_react(), 1);
function PauseCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React243.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React243.createElement("title", {
    id: titleId
  }, title) : null, React243.createElement("path", {
    fillRule: "evenodd",
    d: "M2 10a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm5-2.25A.75.75 0 0 1 7.75 7h.5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75v-4.5Zm4 0a.75.75 0 0 1 .75-.75h.5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75v-4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef243 = React243.forwardRef(PauseCircleIcon);
var PauseCircleIcon_default = ForwardRef243;

// node_modules/@heroicons/react/20/solid/esm/PauseIcon.js
var React244 = __toESM(require_react(), 1);
function PauseIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React244.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React244.createElement("title", {
    id: titleId
  }, title) : null, React244.createElement("path", {
    d: "M5.75 3a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h1.5a.75.75 0 0 0 .75-.75V3.75A.75.75 0 0 0 7.25 3h-1.5ZM12.75 3a.75.75 0 0 0-.75.75v12.5c0 .414.336.75.75.75h1.5a.75.75 0 0 0 .75-.75V3.75a.75.75 0 0 0-.75-.75h-1.5Z"
  }));
}
var ForwardRef244 = React244.forwardRef(PauseIcon);
var PauseIcon_default = ForwardRef244;

// node_modules/@heroicons/react/20/solid/esm/PencilSquareIcon.js
var React245 = __toESM(require_react(), 1);
function PencilSquareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React245.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React245.createElement("title", {
    id: titleId
  }, title) : null, React245.createElement("path", {
    d: "m5.433 13.917 1.262-3.155A4 4 0 0 1 7.58 9.42l6.92-6.918a2.121 2.121 0 0 1 3 3l-6.92 6.918c-.383.383-.84.685-1.343.886l-3.154 1.262a.5.5 0 0 1-.65-.65Z"
  }), React245.createElement("path", {
    d: "M3.5 5.75c0-.69.56-1.25 1.25-1.25H10A.75.75 0 0 0 10 3H4.75A2.75 2.75 0 0 0 2 5.75v9.5A2.75 2.75 0 0 0 4.75 18h9.5A2.75 2.75 0 0 0 17 15.25V10a.75.75 0 0 0-1.5 0v5.25c0 .69-.56 1.25-1.25 1.25h-9.5c-.69 0-1.25-.56-1.25-1.25v-9.5Z"
  }));
}
var ForwardRef245 = React245.forwardRef(PencilSquareIcon);
var PencilSquareIcon_default = ForwardRef245;

// node_modules/@heroicons/react/20/solid/esm/PencilIcon.js
var React246 = __toESM(require_react(), 1);
function PencilIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React246.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React246.createElement("title", {
    id: titleId
  }, title) : null, React246.createElement("path", {
    d: "m2.695 14.762-1.262 3.155a.5.5 0 0 0 .65.65l3.155-1.262a4 4 0 0 0 1.343-.886L17.5 5.501a2.121 2.121 0 0 0-3-3L3.58 13.419a4 4 0 0 0-.885 1.343Z"
  }));
}
var ForwardRef246 = React246.forwardRef(PencilIcon);
var PencilIcon_default = ForwardRef246;

// node_modules/@heroicons/react/20/solid/esm/PercentBadgeIcon.js
var React247 = __toESM(require_react(), 1);
function PercentBadgeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React247.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React247.createElement("title", {
    id: titleId
  }, title) : null, React247.createElement("path", {
    fillRule: "evenodd",
    d: "M3.597 7.348a3 3 0 0 0 0 5.304 3 3 0 0 0 3.75 3.751 3 3 0 0 0 5.305 0 3 3 0 0 0 3.751-3.75 3 3 0 0 0 0-5.305 3 3 0 0 0-3.75-3.751 3 3 0 0 0-5.305 0 3 3 0 0 0-3.751 3.75Zm9.933.182a.75.75 0 0 0-1.06-1.06l-6 6a.75.75 0 1 0 1.06 1.06l6-6Zm.47 5.22a1.25 1.25 0 1 1-2.5 0 1.25 1.25 0 0 1 2.5 0ZM7.25 8.5a1.25 1.25 0 1 0 0-2.5 1.25 1.25 0 0 0 0 2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef247 = React247.forwardRef(PercentBadgeIcon);
var PercentBadgeIcon_default = ForwardRef247;

// node_modules/@heroicons/react/20/solid/esm/PhoneArrowDownLeftIcon.js
var React248 = __toESM(require_react(), 1);
function PhoneArrowDownLeftIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React248.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React248.createElement("title", {
    id: titleId
  }, title) : null, React248.createElement("path", {
    d: "M3.5 2A1.5 1.5 0 0 0 2 3.5V5c0 1.149.15 2.263.43 3.326a13.022 13.022 0 0 0 9.244 9.244c1.063.28 2.177.43 3.326.43h1.5a1.5 1.5 0 0 0 1.5-1.5v-1.148a1.5 1.5 0 0 0-1.175-1.465l-3.223-.716a1.5 1.5 0 0 0-1.767 1.052l-.267.933c-.117.41-.555.643-.95.48a11.542 11.542 0 0 1-6.254-6.254c-.163-.395.07-.833.48-.95l.933-.267a1.5 1.5 0 0 0 1.052-1.767l-.716-3.223A1.5 1.5 0 0 0 4.648 2H3.5ZM16.72 2.22a.75.75 0 1 1 1.06 1.06L14.56 6.5h2.69a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1-.75-.75v-4.5a.75.75 0 0 1 1.5 0v2.69l3.22-3.22Z"
  }));
}
var ForwardRef248 = React248.forwardRef(PhoneArrowDownLeftIcon);
var PhoneArrowDownLeftIcon_default = ForwardRef248;

// node_modules/@heroicons/react/20/solid/esm/PhoneArrowUpRightIcon.js
var React249 = __toESM(require_react(), 1);
function PhoneArrowUpRightIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React249.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React249.createElement("title", {
    id: titleId
  }, title) : null, React249.createElement("path", {
    d: "M3.5 2A1.5 1.5 0 0 0 2 3.5V5c0 1.149.15 2.263.43 3.326a13.022 13.022 0 0 0 9.244 9.244c1.063.28 2.177.43 3.326.43h1.5a1.5 1.5 0 0 0 1.5-1.5v-1.148a1.5 1.5 0 0 0-1.175-1.465l-3.223-.716a1.5 1.5 0 0 0-1.767 1.052l-.267.933c-.117.41-.555.643-.95.48a11.542 11.542 0 0 1-6.254-6.254c-.163-.395.07-.833.48-.95l.933-.267a1.5 1.5 0 0 0 1.052-1.767l-.716-3.223A1.5 1.5 0 0 0 4.648 2H3.5ZM16.5 4.56l-3.22 3.22a.75.75 0 1 1-1.06-1.06l3.22-3.22h-2.69a.75.75 0 0 1 0-1.5h4.5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-1.5 0V4.56Z"
  }));
}
var ForwardRef249 = React249.forwardRef(PhoneArrowUpRightIcon);
var PhoneArrowUpRightIcon_default = ForwardRef249;

// node_modules/@heroicons/react/20/solid/esm/PhoneXMarkIcon.js
var React250 = __toESM(require_react(), 1);
function PhoneXMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React250.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React250.createElement("title", {
    id: titleId
  }, title) : null, React250.createElement("path", {
    fillRule: "evenodd",
    d: "M3.5 2A1.5 1.5 0 0 0 2 3.5V5c0 1.149.15 2.263.43 3.326a13.022 13.022 0 0 0 9.244 9.244c1.063.28 2.177.43 3.326.43h1.5a1.5 1.5 0 0 0 1.5-1.5v-1.148a1.5 1.5 0 0 0-1.175-1.465l-3.223-.716a1.5 1.5 0 0 0-1.767 1.052l-.267.933c-.117.41-.555.643-.95.48a11.542 11.542 0 0 1-6.254-6.254c-.163-.395.07-.833.48-.95l.933-.267a1.5 1.5 0 0 0 1.052-1.767l-.716-3.223A1.5 1.5 0 0 0 4.648 2H3.5Zm9.78.22a.75.75 0 1 0-1.06 1.06L13.94 5l-1.72 1.72a.75.75 0 0 0 1.06 1.06L15 6.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L16.06 5l1.72-1.72a.75.75 0 0 0-1.06-1.06L15 3.94l-1.72-1.72Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef250 = React250.forwardRef(PhoneXMarkIcon);
var PhoneXMarkIcon_default = ForwardRef250;

// node_modules/@heroicons/react/20/solid/esm/PhoneIcon.js
var React251 = __toESM(require_react(), 1);
function PhoneIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React251.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React251.createElement("title", {
    id: titleId
  }, title) : null, React251.createElement("path", {
    fillRule: "evenodd",
    d: "M2 3.5A1.5 1.5 0 0 1 3.5 2h1.148a1.5 1.5 0 0 1 1.465 1.175l.716 3.223a1.5 1.5 0 0 1-1.052 1.767l-.933.267c-.41.117-.643.555-.48.95a11.542 11.542 0 0 0 6.254 6.254c.395.163.833-.07.95-.48l.267-.933a1.5 1.5 0 0 1 1.767-1.052l3.223.716A1.5 1.5 0 0 1 18 15.352V16.5a1.5 1.5 0 0 1-1.5 1.5H15c-1.149 0-2.263-.15-3.326-.43A13.022 13.022 0 0 1 2.43 8.326 13.019 13.019 0 0 1 2 5V3.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef251 = React251.forwardRef(PhoneIcon);
var PhoneIcon_default = ForwardRef251;

// node_modules/@heroicons/react/20/solid/esm/PhotoIcon.js
var React252 = __toESM(require_react(), 1);
function PhotoIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React252.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React252.createElement("title", {
    id: titleId
  }, title) : null, React252.createElement("path", {
    fillRule: "evenodd",
    d: "M1 5.25A2.25 2.25 0 0 1 3.25 3h13.5A2.25 2.25 0 0 1 19 5.25v9.5A2.25 2.25 0 0 1 16.75 17H3.25A2.25 2.25 0 0 1 1 14.75v-9.5Zm1.5 5.81v3.69c0 .414.336.75.75.75h13.5a.75.75 0 0 0 .75-.75v-2.69l-2.22-2.219a.75.75 0 0 0-1.06 0l-1.91 1.909.47.47a.75.75 0 1 1-1.06 1.06L6.53 8.091a.75.75 0 0 0-1.06 0l-2.97 2.97ZM12 7a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef252 = React252.forwardRef(PhotoIcon);
var PhotoIcon_default = ForwardRef252;

// node_modules/@heroicons/react/20/solid/esm/PlayCircleIcon.js
var React253 = __toESM(require_react(), 1);
function PlayCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React253.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React253.createElement("title", {
    id: titleId
  }, title) : null, React253.createElement("path", {
    fillRule: "evenodd",
    d: "M2 10a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm6.39-2.908a.75.75 0 0 1 .766.027l3.5 2.25a.75.75 0 0 1 0 1.262l-3.5 2.25A.75.75 0 0 1 8 12.25v-4.5a.75.75 0 0 1 .39-.658Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef253 = React253.forwardRef(PlayCircleIcon);
var PlayCircleIcon_default = ForwardRef253;

// node_modules/@heroicons/react/20/solid/esm/PlayPauseIcon.js
var React254 = __toESM(require_react(), 1);
function PlayPauseIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React254.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React254.createElement("title", {
    id: titleId
  }, title) : null, React254.createElement("path", {
    d: "M12.75 4a.75.75 0 0 0-.75.75v10.5c0 .414.336.75.75.75h.5a.75.75 0 0 0 .75-.75V4.75a.75.75 0 0 0-.75-.75h-.5ZM17.75 4a.75.75 0 0 0-.75.75v10.5c0 .414.336.75.75.75h.5a.75.75 0 0 0 .75-.75V4.75a.75.75 0 0 0-.75-.75h-.5ZM3.288 4.819A1.5 1.5 0 0 0 1 6.095v7.81a1.5 1.5 0 0 0 2.288 1.277l6.323-3.906a1.5 1.5 0 0 0 0-2.552L3.288 4.819Z"
  }));
}
var ForwardRef254 = React254.forwardRef(PlayPauseIcon);
var PlayPauseIcon_default = ForwardRef254;

// node_modules/@heroicons/react/20/solid/esm/PlayIcon.js
var React255 = __toESM(require_react(), 1);
function PlayIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React255.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React255.createElement("title", {
    id: titleId
  }, title) : null, React255.createElement("path", {
    d: "M6.3 2.84A1.5 1.5 0 0 0 4 4.11v11.78a1.5 1.5 0 0 0 2.3 1.27l9.344-5.891a1.5 1.5 0 0 0 0-2.538L6.3 2.841Z"
  }));
}
var ForwardRef255 = React255.forwardRef(PlayIcon);
var PlayIcon_default = ForwardRef255;

// node_modules/@heroicons/react/20/solid/esm/PlusCircleIcon.js
var React256 = __toESM(require_react(), 1);
function PlusCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React256.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React256.createElement("title", {
    id: titleId
  }, title) : null, React256.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16Zm.75-11.25a.75.75 0 0 0-1.5 0v2.5h-2.5a.75.75 0 0 0 0 1.5h2.5v2.5a.75.75 0 0 0 1.5 0v-2.5h2.5a.75.75 0 0 0 0-1.5h-2.5v-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef256 = React256.forwardRef(PlusCircleIcon);
var PlusCircleIcon_default = ForwardRef256;

// node_modules/@heroicons/react/20/solid/esm/PlusSmallIcon.js
var React257 = __toESM(require_react(), 1);
function PlusSmallIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React257.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React257.createElement("title", {
    id: titleId
  }, title) : null, React257.createElement("path", {
    d: "M10.75 6.75a.75.75 0 0 0-1.5 0v2.5h-2.5a.75.75 0 0 0 0 1.5h2.5v2.5a.75.75 0 0 0 1.5 0v-2.5h2.5a.75.75 0 0 0 0-1.5h-2.5v-2.5Z"
  }));
}
var ForwardRef257 = React257.forwardRef(PlusSmallIcon);
var PlusSmallIcon_default = ForwardRef257;

// node_modules/@heroicons/react/20/solid/esm/PlusIcon.js
var React258 = __toESM(require_react(), 1);
function PlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React258.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React258.createElement("title", {
    id: titleId
  }, title) : null, React258.createElement("path", {
    d: "M10.75 4.75a.75.75 0 0 0-1.5 0v4.5h-4.5a.75.75 0 0 0 0 1.5h4.5v4.5a.75.75 0 0 0 1.5 0v-4.5h4.5a.75.75 0 0 0 0-1.5h-4.5v-4.5Z"
  }));
}
var ForwardRef258 = React258.forwardRef(PlusIcon);
var PlusIcon_default = ForwardRef258;

// node_modules/@heroicons/react/20/solid/esm/PowerIcon.js
var React259 = __toESM(require_react(), 1);
function PowerIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React259.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React259.createElement("title", {
    id: titleId
  }, title) : null, React259.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a.75.75 0 0 1 .75.75v7.5a.75.75 0 0 1-1.5 0v-7.5A.75.75 0 0 1 10 2ZM5.404 4.343a.75.75 0 0 1 0 1.06 6.5 6.5 0 1 0 9.192 0 .75.75 0 1 1 1.06-1.06 8 8 0 1 1-11.313 0 .75.75 0 0 1 1.06 0Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef259 = React259.forwardRef(PowerIcon);
var PowerIcon_default = ForwardRef259;

// node_modules/@heroicons/react/20/solid/esm/PresentationChartBarIcon.js
var React260 = __toESM(require_react(), 1);
function PresentationChartBarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React260.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React260.createElement("title", {
    id: titleId
  }, title) : null, React260.createElement("path", {
    fillRule: "evenodd",
    d: "M1 2.75A.75.75 0 0 1 1.75 2h16.5a.75.75 0 0 1 0 1.5H18v8.75A2.75 2.75 0 0 1 15.25 15h-1.072l.798 3.06a.75.75 0 0 1-1.452.38L13.41 18H6.59l-.114.44a.75.75 0 0 1-1.452-.38L5.823 15H4.75A2.75 2.75 0 0 1 2 12.25V3.5h-.25A.75.75 0 0 1 1 2.75ZM7.373 15l-.391 1.5h6.037l-.392-1.5H7.373ZM13.25 5a.75.75 0 0 1 .75.75v5.5a.75.75 0 0 1-1.5 0v-5.5a.75.75 0 0 1 .75-.75Zm-6.5 4a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 6.75 9Zm4-1.25a.75.75 0 0 0-1.5 0v3.5a.75.75 0 0 0 1.5 0v-3.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef260 = React260.forwardRef(PresentationChartBarIcon);
var PresentationChartBarIcon_default = ForwardRef260;

// node_modules/@heroicons/react/20/solid/esm/PresentationChartLineIcon.js
var React261 = __toESM(require_react(), 1);
function PresentationChartLineIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React261.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React261.createElement("title", {
    id: titleId
  }, title) : null, React261.createElement("path", {
    fillRule: "evenodd",
    d: "M1 2.75A.75.75 0 0 1 1.75 2h16.5a.75.75 0 0 1 0 1.5H18v8.75A2.75 2.75 0 0 1 15.25 15h-1.072l.798 3.06a.75.75 0 0 1-1.452.38L13.41 18H6.59l-.114.44a.75.75 0 0 1-1.452-.38L5.823 15H4.75A2.75 2.75 0 0 1 2 12.25V3.5h-.25A.75.75 0 0 1 1 2.75ZM7.373 15l-.391 1.5h6.037l-.392-1.5H7.373Zm7.49-8.931a.75.75 0 0 1-.175 1.046 19.326 19.326 0 0 0-3.398 *********** 0 0 1-1.097.04L8.5 8.561l-2.22 2.22A.75.75 0 1 1 5.22 9.72l2.75-2.75a.75.75 0 0 1 1.06 0l1.664 1.663a20.786 20.786 0 0 1 3.122-********** 0 0 1 1.046.176Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef261 = React261.forwardRef(PresentationChartLineIcon);
var PresentationChartLineIcon_default = ForwardRef261;

// node_modules/@heroicons/react/20/solid/esm/PrinterIcon.js
var React262 = __toESM(require_react(), 1);
function PrinterIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React262.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React262.createElement("title", {
    id: titleId
  }, title) : null, React262.createElement("path", {
    fillRule: "evenodd",
    d: "M5 2.75C5 1.784 5.784 1 6.75 1h6.5c.966 0 1.75.784 1.75 1.75v3.552c.377.046.752.097 1.126.153A2.212 2.212 0 0 1 18 8.653v4.097A2.25 2.25 0 0 1 15.75 15h-.241l.305 1.984A1.75 1.75 0 0 1 14.084 19H5.915a1.75 1.75 0 0 1-1.73-2.016L4.492 15H4.25A2.25 2.25 0 0 1 2 12.75V8.653c0-1.082.775-2.034 1.874-2.198.374-.056.75-.107 1.127-.153L5 6.25v-3.5Zm8.5 3.397a41.533 41.533 0 0 0-7 0V2.75a.25.25 0 0 1 .25-.25h6.5a.25.25 0 0 1 .25.25v3.397ZM6.608 12.5a.25.25 0 0 0-.247.212l-.693 4.5a.25.25 0 0 0 .247.288h8.17a.25.25 0 0 0 .246-.288l-.692-4.5a.25.25 0 0 0-.247-.212H6.608Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef262 = React262.forwardRef(PrinterIcon);
var PrinterIcon_default = ForwardRef262;

// node_modules/@heroicons/react/20/solid/esm/PuzzlePieceIcon.js
var React263 = __toESM(require_react(), 1);
function PuzzlePieceIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React263.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React263.createElement("title", {
    id: titleId
  }, title) : null, React263.createElement("path", {
    d: "M12 4.467c0-.405.262-.75.559-1.027.276-.257.441-.584.441-.94 0-.828-.895-1.5-2-1.5s-2 .672-2 1.5c0 .362.171.694.456.953.29.265.544.6.544.994a.968.968 0 0 1-1.024.974 39.655 39.655 0 0 1-3.014-.306.75.75 0 0 0-.847.847c.14.993.242 1.999.306 3.014A.968.968 0 0 1 4.447 10c-.393 0-.729-.253-.994-.544C3.194 9.17 2.862 9 2.5 9 1.672 9 1 9.895 1 11s.672 2 1.5 2c.356 0 .683-.165.94-.441.276-.297.622-.559 1.027-.559a.997.997 0 0 1 1.004 1.03 39.747 39.747 0 0 1-.319 3.734.75.75 0 0 0 .64.842c1.05.146 2.111.252 3.184.318A.97.97 0 0 0 10 16.948c0-.394-.254-.73-.545-.995C9.171 15.693 9 15.362 9 15c0-.828.895-1.5 2-1.5s2 .672 2 1.5c0 .356-.165.683-.441.94-.297.276-.559.622-.559 1.027a.998.998 0 0 0 1.03 1.005c1.337-.05 2.659-.162 3.961-.337a.75.75 0 0 0 .644-.644c.175-1.302.288-2.624.337-3.961A.998.998 0 0 0 16.967 12c-.405 0-.75.262-1.027.559-.257.276-.584.441-.94.441-.828 0-1.5-.895-1.5-2s.672-2 1.5-2c.362 0 .694.17.953.455.265.291.601.545.995.545a.97.97 0 0 0 .976-1.024 41.159 41.159 0 0 0-.318-3.184.75.75 0 0 0-.842-.64c-1.228.164-2.473.271-3.734.319A.997.997 0 0 1 12 4.467Z"
  }));
}
var ForwardRef263 = React263.forwardRef(PuzzlePieceIcon);
var PuzzlePieceIcon_default = ForwardRef263;

// node_modules/@heroicons/react/20/solid/esm/QrCodeIcon.js
var React264 = __toESM(require_react(), 1);
function QrCodeIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React264.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React264.createElement("title", {
    id: titleId
  }, title) : null, React264.createElement("path", {
    fillRule: "evenodd",
    d: "M3.75 2A1.75 1.75 0 0 0 2 3.75v3.5C2 8.216 2.784 9 3.75 9h3.5A1.75 1.75 0 0 0 9 7.25v-3.5A1.75 1.75 0 0 0 7.25 2h-3.5ZM3.5 3.75a.25.25 0 0 1 .25-.25h3.5a.25.25 0 0 1 .25.25v3.5a.25.25 0 0 1-.25.25h-3.5a.25.25 0 0 1-.25-.25v-3.5ZM3.75 11A1.75 1.75 0 0 0 2 12.75v3.5c0 .966.784 1.75 1.75 1.75h3.5A1.75 1.75 0 0 0 9 16.25v-3.5A1.75 1.75 0 0 0 7.25 11h-3.5Zm-.25 1.75a.25.25 0 0 1 .25-.25h3.5a.25.25 0 0 1 .25.25v3.5a.25.25 0 0 1-.25.25h-3.5a.25.25 0 0 1-.25-.25v-3.5Zm7.5-9c0-.966.784-1.75 1.75-1.75h3.5c.966 0 1.75.784 1.75 1.75v3.5A1.75 1.75 0 0 1 16.25 9h-3.5A1.75 1.75 0 0 1 11 7.25v-3.5Zm1.75-.25a.25.25 0 0 0-.25.25v3.5c0 .*************.25h3.5a.25.25 0 0 0 .25-.25v-3.5a.25.25 0 0 0-.25-.25h-3.5Zm-7.26 1a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1h.01a1 1 0 0 0 1-1V5.5a1 1 0 0 0-1-1h-.01Zm9 0a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1h.01a1 1 0 0 0 1-1V5.5a1 1 0 0 0-1-1h-.01Zm-9 9a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1h.01a1 1 0 0 0 1-1v-.01a1 1 0 0 0-1-1h-.01Zm9 0a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1h.01a1 1 0 0 0 1-1v-.01a1 1 0 0 0-1-1h-.01Zm-3.5-1.5a1 1 0 0 1 1-1H12a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1V12Zm6-1a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1H17a1 1 0 0 0 1-1V12a1 1 0 0 0-1-1h-.01Zm-1 6a1 1 0 0 1 1-1H17a1 1 0 0 1 1 1v.01a1 1 0 0 1-1 1h-.01a1 1 0 0 1-1-1V17Zm-4-1a1 1 0 0 0-1 1v.01a1 1 0 0 0 1 1H12a1 1 0 0 0 1-1V17a1 1 0 0 0-1-1h-.01Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef264 = React264.forwardRef(QrCodeIcon);
var QrCodeIcon_default = ForwardRef264;

// node_modules/@heroicons/react/20/solid/esm/QuestionMarkCircleIcon.js
var React265 = __toESM(require_react(), 1);
function QuestionMarkCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React265.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React265.createElement("title", {
    id: titleId
  }, title) : null, React265.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0ZM8.94 6.94a.75.75 0 1 1-1.061-1.061 3 3 0 1 1 2.871 5.026v.345a.75.75 0 0 1-1.5 0v-.5c0-.72.57-1.172 1.081-1.287A1.5 1.5 0 1 0 8.94 6.94ZM10 15a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef265 = React265.forwardRef(QuestionMarkCircleIcon);
var QuestionMarkCircleIcon_default = ForwardRef265;

// node_modules/@heroicons/react/20/solid/esm/QueueListIcon.js
var React266 = __toESM(require_react(), 1);
function QueueListIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React266.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React266.createElement("title", {
    id: titleId
  }, title) : null, React266.createElement("path", {
    d: "M2 4.5A2.5 2.5 0 0 1 4.5 2h11a2.5 2.5 0 0 1 0 5h-11A2.5 2.5 0 0 1 2 4.5ZM2.75 9.083a.75.75 0 0 0 0 1.5h14.5a.75.75 0 0 0 0-1.5H2.75ZM2.75 12.663a.75.75 0 0 0 0 1.5h14.5a.75.75 0 0 0 0-1.5H2.75ZM2.75 16.25a.75.75 0 0 0 0 1.5h14.5a.75.75 0 1 0 0-1.5H2.75Z"
  }));
}
var ForwardRef266 = React266.forwardRef(QueueListIcon);
var QueueListIcon_default = ForwardRef266;

// node_modules/@heroicons/react/20/solid/esm/RadioIcon.js
var React267 = __toESM(require_react(), 1);
function RadioIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React267.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React267.createElement("title", {
    id: titleId
  }, title) : null, React267.createElement("path", {
    fillRule: "evenodd",
    d: "M17.45 3.473a.75.75 0 1 0-.4-1.446L5.313 5.265c-.84.096-1.671.217-2.495.362A2.212 2.212 0 0 0 1 7.816v7.934A2.25 2.25 0 0 0 3.25 18h13.5A2.25 2.25 0 0 0 19 15.75V7.816c0-1.06-.745-2-1.817-2.189a41.12 41.12 0 0 0-5.406-.59l5.673-1.564ZM16 9.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM14.5 16a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Zm-9.26-5a.75.75 0 0 1 .75-.75H6a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V11Zm2.75-.75a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75H8a.75.75 0 0 0 .75-.75V11a.75.75 0 0 0-.75-.75h-.01Zm-1.75-1.5A.75.75 0 0 1 6.99 8H7a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm3.583.42a.75.75 0 0 0-1.06 0l-.007.007a.75.75 0 0 0 0 1.06l.007.007a.75.75 0 0 0 1.06 0l.007-.007a.75.75 0 0 0 0-1.06l-.007-.008Zm.427 2.08A.75.75 0 0 1 11 12v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V12a.75.75 0 0 1 .75-.75h.01Zm-.42 3.583a.75.75 0 0 0 0-1.06l-.007-.007a.75.75 0 0 0-1.06 0l-.007.007a.75.75 0 0 0 0 1.06l.007.008a.75.75 0 0 0 1.06 0l.008-.008Zm-3.59.417a.75.75 0 0 1 .75-.75H7a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75v-.01Zm-1.013-1.484a.75.75 0 0 0-1.06 0l-.008.007a.75.75 0 0 0 0 1.06l.007.008a.75.75 0 0 0 1.061 0l.007-.008a.75.75 0 0 0 0-1.06l-.007-.007ZM3.75 11.25a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V12a.75.75 0 0 1 .75-.75h.01Zm1.484-1.013a.75.75 0 0 0 0-1.06l-.007-.007a.75.75 0 0 0-1.06 0l-.007.007a.75.75 0 0 0 0 1.06l.007.007a.75.75 0 0 0 1.06 0l.007-.007ZM7.24 13a.75.75 0 0 1 .75-.75H8a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V13Zm-1.25-.75a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75V13a.75.75 0 0 0-.75-.75h-.01Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef267 = React267.forwardRef(RadioIcon);
var RadioIcon_default = ForwardRef267;

// node_modules/@heroicons/react/20/solid/esm/ReceiptPercentIcon.js
var React268 = __toESM(require_react(), 1);
function ReceiptPercentIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React268.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React268.createElement("title", {
    id: titleId
  }, title) : null, React268.createElement("path", {
    fillRule: "evenodd",
    d: "M4.93 2.31a41.401 41.401 0 0 1 10.14 0C16.194 2.45 17 3.414 17 4.517V17.25a.75.75 0 0 1-1.075.676l-2.8-1.344-2.8 1.344a.75.75 0 0 1-.65 0l-2.8-1.344-2.8 1.344A.75.75 0 0 1 3 17.25V4.517c0-1.103.806-2.068 1.93-2.207Zm8.85 4.97a.75.75 0 0 0-1.06-1.06l-6.5 6.5a.75.75 0 1 0 1.06 1.06l6.5-6.5ZM9 8a1 1 0 1 1-2 0 1 1 0 0 1 2 0Zm3 5a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef268 = React268.forwardRef(ReceiptPercentIcon);
var ReceiptPercentIcon_default = ForwardRef268;

// node_modules/@heroicons/react/20/solid/esm/ReceiptRefundIcon.js
var React269 = __toESM(require_react(), 1);
function ReceiptRefundIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React269.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React269.createElement("title", {
    id: titleId
  }, title) : null, React269.createElement("path", {
    fillRule: "evenodd",
    d: "M4.93 2.31a41.401 41.401 0 0 1 10.14 0C16.194 2.45 17 3.414 17 4.517V17.25a.75.75 0 0 1-1.075.676l-2.8-1.344-2.8 1.344a.75.75 0 0 1-.65 0l-2.8-1.344-2.8 1.344A.75.75 0 0 1 3 17.25V4.517c0-1.103.806-2.068 1.93-2.207Zm4.822 3.997a.75.75 0 1 0-1.004-1.114l-2.5 2.25a.75.75 0 0 0 0 1.114l2.5 2.25a.75.75 0 0 0 1.004-1.114L8.704 8.75h1.921a1.875 1.875 0 0 1 0 ********** 0 0 0 0 1.5 3.375 3.375 0 1 0 0-6.75h-1.92l1.047-.943Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef269 = React269.forwardRef(ReceiptRefundIcon);
var ReceiptRefundIcon_default = ForwardRef269;

// node_modules/@heroicons/react/20/solid/esm/RectangleGroupIcon.js
var React270 = __toESM(require_react(), 1);
function RectangleGroupIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React270.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React270.createElement("title", {
    id: titleId
  }, title) : null, React270.createElement("path", {
    fillRule: "evenodd",
    d: "M2.5 3A1.5 1.5 0 0 0 1 4.5v4A1.5 1.5 0 0 0 2.5 10h6A1.5 1.5 0 0 0 10 8.5v-4A1.5 1.5 0 0 0 8.5 3h-6Zm11 2A1.5 1.5 0 0 0 12 6.5v7a1.5 1.5 0 0 0 1.5 1.5h4a1.5 1.5 0 0 0 1.5-1.5v-7A1.5 1.5 0 0 0 17.5 5h-4Zm-10 7A1.5 1.5 0 0 0 2 13.5v2A1.5 1.5 0 0 0 3.5 17h6a1.5 1.5 0 0 0 1.5-1.5v-2A1.5 1.5 0 0 0 9.5 12h-6Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef270 = React270.forwardRef(RectangleGroupIcon);
var RectangleGroupIcon_default = ForwardRef270;

// node_modules/@heroicons/react/20/solid/esm/RectangleStackIcon.js
var React271 = __toESM(require_react(), 1);
function RectangleStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React271.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React271.createElement("title", {
    id: titleId
  }, title) : null, React271.createElement("path", {
    d: "M5.127 3.502 5.25 3.5h9.5c.041 0 .082 0 .123.002A2.251 2.251 0 0 0 12.75 2h-5.5a2.25 2.25 0 0 0-2.123 1.502ZM1 10.25A2.25 2.25 0 0 1 3.25 8h13.5A2.25 2.25 0 0 1 19 10.25v5.5A2.25 2.25 0 0 1 16.75 18H3.25A2.25 2.25 0 0 1 1 15.75v-5.5ZM3.25 6.5c-.04 0-.082 0-.123.002A2.25 2.25 0 0 1 5.25 5h9.5c.98 0 1.814.627 2.123 1.502a3.819 3.819 0 0 0-.123-.002H3.25Z"
  }));
}
var ForwardRef271 = React271.forwardRef(RectangleStackIcon);
var RectangleStackIcon_default = ForwardRef271;

// node_modules/@heroicons/react/20/solid/esm/RocketLaunchIcon.js
var React272 = __toESM(require_react(), 1);
function RocketLaunchIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React272.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React272.createElement("title", {
    id: titleId
  }, title) : null, React272.createElement("path", {
    fillRule: "evenodd",
    d: "M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z",
    clipRule: "evenodd"
  }), React272.createElement("path", {
    fillRule: "evenodd",
    d: "M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef272 = React272.forwardRef(RocketLaunchIcon);
var RocketLaunchIcon_default = ForwardRef272;

// node_modules/@heroicons/react/20/solid/esm/RssIcon.js
var React273 = __toESM(require_react(), 1);
function RssIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React273.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React273.createElement("title", {
    id: titleId
  }, title) : null, React273.createElement("path", {
    d: "M3.75 3a.75.75 0 0 0-.75.75v.5c0 .414.336.75.75.75H4c6.075 0 11 4.925 11 11v.25c0 .414.336.75.75.75h.5a.75.75 0 0 0 .75-.75V16C17 8.82 11.18 3 4 3h-.25Z"
  }), React273.createElement("path", {
    d: "M3 8.75A.75.75 0 0 1 3.75 8H4a8 8 0 0 1 8 8v.25a.75.75 0 0 1-.75.75h-.5a.75.75 0 0 1-.75-.75V16a6 6 0 0 0-6-6h-.25A.75.75 0 0 1 3 9.25v-.5ZM7 15a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"
  }));
}
var ForwardRef273 = React273.forwardRef(RssIcon);
var RssIcon_default = ForwardRef273;

// node_modules/@heroicons/react/20/solid/esm/ScaleIcon.js
var React274 = __toESM(require_react(), 1);
function ScaleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React274.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React274.createElement("title", {
    id: titleId
  }, title) : null, React274.createElement("path", {
    fillRule: "evenodd",
    d: "M10 2a.75.75 0 0 1 .75.75v.258a33.186 33.186 0 0 1 6.668.83.75.75 0 0 1-.336 1.461 31.28 31.28 0 0 0-1.103-.232l1.702 7.545a.75.75 0 0 1-.387.832A4.981 4.981 0 0 1 15 14c-.825 0-1.606-.2-2.294-.556a.75.75 0 0 1-.387-.832l1.77-7.849a31.743 31.743 0 0 0-3.339-.254v11.505a20.01 20.01 0 0 1 3.78.501.75.75 0 1 1-.339 1.462A18.558 18.558 0 0 0 10 17.5c-1.442 0-2.845.165-4.191.477a.75.75 0 0 1-.338-1.462 20.01 20.01 0 0 1 3.779-.501V4.509c-1.129.026-2.243.112-3.34.254l1.771 7.85a.75.75 0 0 1-.387.83A4.98 4.98 0 0 1 5 14a4.98 4.98 0 0 1-2.294-.556.75.75 0 0 1-.387-.832L4.02 5.067c-.37.07-.738.148-1.103.232a.75.75 0 0 1-.336-1.462 32.845 32.845 0 0 1 6.668-.829V2.75A.75.75 0 0 1 10 2ZM5 7.543 3.92 12.33a3.499 3.499 0 0 0 2.16 0L5 7.543Zm10 0-1.08 4.787a3.498 3.498 0 0 0 2.16 0L15 7.543Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef274 = React274.forwardRef(ScaleIcon);
var ScaleIcon_default = ForwardRef274;

// node_modules/@heroicons/react/20/solid/esm/ScissorsIcon.js
var React275 = __toESM(require_react(), 1);
function ScissorsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React275.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React275.createElement("title", {
    id: titleId
  }, title) : null, React275.createElement("path", {
    fillRule: "evenodd",
    d: "M1.469 3.75a3.5 3.5 0 0 0 5.617 4.11l.883.51c.***************.21.043.15-.176.318-.338.5-.484.286-.23.3-.709-.018-.892l-.825-.477A3.501 3.501 0 0 0 1.47 3.75Zm2.03 3.482a2 2 0 1 1 2-3.464 2 2 0 0 1-2 3.464ZM9.956 8.322a2.75 2.75 0 0 0-1.588 1.822L7.97 11.63l-.884.51A3.501 3.501 0 0 0 1.47 16.25a3.5 3.5 0 0 0 6.367-2.81l10.68-6.166a.75.75 0 0 0-.182-1.373l-.703-.189a2.75 2.75 0 0 0-1.78.123L9.955 8.322ZM2.768 15.5a2 2 0 1 1 3.464-2 2 2 0 0 1-3.464 2Z",
    clipRule: "evenodd"
  }), React275.createElement("path", {
    d: "M12.52 11.89a.5.5 0 0 0 .056.894l3.274 1.381a2.75 2.75 0 0 0 1.78.123l.704-.189a.75.75 0 0 0 .18-1.373l-3.47-2.004a.5.5 0 0 0-.5 0L12.52 11.89Z"
  }));
}
var ForwardRef275 = React275.forwardRef(ScissorsIcon);
var ScissorsIcon_default = ForwardRef275;

// node_modules/@heroicons/react/20/solid/esm/ServerStackIcon.js
var React276 = __toESM(require_react(), 1);
function ServerStackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React276.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React276.createElement("title", {
    id: titleId
  }, title) : null, React276.createElement("path", {
    d: "M4.464 3.162A2 2 0 0 1 6.28 2h7.44a2 2 0 0 1 1.816 1.162l1.154 2.5c.067.145.115.291.145.438A3.508 3.508 0 0 0 16 6H4c-.288 0-.568.035-.835.1.03-.147.078-.293.145-.438l1.154-2.5Z"
  }), React276.createElement("path", {
    fillRule: "evenodd",
    d: "M2 9.5a2 2 0 0 1 2-2h12a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Zm13.24 0a.75.75 0 0 1 .75-.75H16a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V9.5Zm-2.25-.75a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75H13a.75.75 0 0 0 .75-.75V9.5a.75.75 0 0 0-.75-.75h-.01ZM2 15a2 2 0 0 1 2-2h12a2 2 0 1 1 0 4H4a2 2 0 0 1-2-2Zm13.24 0a.75.75 0 0 1 .75-.75H16a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V15Zm-2.25-.75a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75H13a.75.75 0 0 0 .75-.75V15a.75.75 0 0 0-.75-.75h-.01Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef276 = React276.forwardRef(ServerStackIcon);
var ServerStackIcon_default = ForwardRef276;

// node_modules/@heroicons/react/20/solid/esm/ServerIcon.js
var React277 = __toESM(require_react(), 1);
function ServerIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React277.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React277.createElement("title", {
    id: titleId
  }, title) : null, React277.createElement("path", {
    d: "M4.632 3.533A2 2 0 0 1 6.577 2h6.846a2 2 0 0 1 1.945 1.533l1.976 8.234A3.489 3.489 0 0 0 16 11.5H4c-.476 0-.93.095-1.344.267l1.976-8.234Z"
  }), React277.createElement("path", {
    fillRule: "evenodd",
    d: "M4 13a2 2 0 1 0 0 4h12a2 2 0 1 0 0-4H4Zm11.24 2a.75.75 0 0 1 .75-.75H16a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75h-.01a.75.75 0 0 1-.75-.75V15Zm-2.25-.75a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75H13a.75.75 0 0 0 .75-.75V15a.75.75 0 0 0-.75-.75h-.01Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef277 = React277.forwardRef(ServerIcon);
var ServerIcon_default = ForwardRef277;

// node_modules/@heroicons/react/20/solid/esm/ShareIcon.js
var React278 = __toESM(require_react(), 1);
function ShareIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React278.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React278.createElement("title", {
    id: titleId
  }, title) : null, React278.createElement("path", {
    d: "M13 4.5a2.5 2.5 0 1 1 .702 1.737L6.97 9.604a2.518 2.518 0 0 1 0 .792l6.733 3.367a2.5 2.5 0 1 1-.671 1.341l-6.733-3.367a2.5 2.5 0 1 1 0-3.475l6.733-3.366A2.52 2.52 0 0 1 13 4.5Z"
  }));
}
var ForwardRef278 = React278.forwardRef(ShareIcon);
var ShareIcon_default = ForwardRef278;

// node_modules/@heroicons/react/20/solid/esm/ShieldCheckIcon.js
var React279 = __toESM(require_react(), 1);
function ShieldCheckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React279.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React279.createElement("title", {
    id: titleId
  }, title) : null, React279.createElement("path", {
    fillRule: "evenodd",
    d: "M9.661 2.237a.531.531 0 0 1 .678 0 11.947 11.947 0 0 0 7.078 2.749.5.5 0 0 1 .479.425c.069.52.104 1.05.104 1.59 0 5.162-3.26 9.563-7.834 11.256a.48.48 0 0 1-.332 0C5.26 16.564 2 12.163 2 7c0-.538.035-1.069.104-1.589a.5.5 0 0 1 .48-.425 11.947 11.947 0 0 0 7.077-2.75Zm4.196 5.954a.75.75 0 0 0-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 1 0-1.06 1.061l2.5 2.5a.75.75 0 0 0 1.137-.089l4-5.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef279 = React279.forwardRef(ShieldCheckIcon);
var ShieldCheckIcon_default = ForwardRef279;

// node_modules/@heroicons/react/20/solid/esm/ShieldExclamationIcon.js
var React280 = __toESM(require_react(), 1);
function ShieldExclamationIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React280.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React280.createElement("title", {
    id: titleId
  }, title) : null, React280.createElement("path", {
    fillRule: "evenodd",
    d: "M10.339 2.237a.531.531 0 0 0-.678 0 11.947 11.947 0 0 1-7.078 ******** 0 0 0-.479.425A12.11 12.11 0 0 0 2 7c0 5.163 3.26 9.564 7.834 11.257a.48.48 0 0 0 .332 0C14.74 16.564 18 12.163 18 7c0-.538-.035-1.069-.104-1.589a.5.5 0 0 0-.48-.425 11.947 11.947 0 0 1-7.077-2.75ZM10 6a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 10 6Zm0 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef280 = React280.forwardRef(ShieldExclamationIcon);
var ShieldExclamationIcon_default = ForwardRef280;

// node_modules/@heroicons/react/20/solid/esm/ShoppingBagIcon.js
var React281 = __toESM(require_react(), 1);
function ShoppingBagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React281.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React281.createElement("title", {
    id: titleId
  }, title) : null, React281.createElement("path", {
    fillRule: "evenodd",
    d: "M6 5v1H4.667a1.75 1.75 0 0 0-1.743 1.598l-.826 9.5A1.75 1.75 0 0 0 3.84 19H16.16a1.75 1.75 0 0 0 1.743-1.902l-.826-9.5A1.75 1.75 0 0 0 15.333 6H14V5a4 4 0 0 0-8 0Zm4-2.5A2.5 2.5 0 0 0 7.5 5v1h5V5A2.5 2.5 0 0 0 10 2.5ZM7.5 10a2.5 2.5 0 0 0 5 0V8.75a.75.75 0 0 1 1.5 0V10a4 4 0 0 1-8 0V8.75a.75.75 0 0 1 1.5 0V10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef281 = React281.forwardRef(ShoppingBagIcon);
var ShoppingBagIcon_default = ForwardRef281;

// node_modules/@heroicons/react/20/solid/esm/ShoppingCartIcon.js
var React282 = __toESM(require_react(), 1);
function ShoppingCartIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React282.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React282.createElement("title", {
    id: titleId
  }, title) : null, React282.createElement("path", {
    d: "M1 1.75A.75.75 0 0 1 1.75 1h1.628a1.75 1.75 0 0 1 1.734 1.51L5.18 3a65.25 65.25 0 0 1 13.36 1.412.75.75 0 0 1 .58.875 48.645 48.645 0 0 1-1.618 ********* 0 0 1-.712.513H6a2.503 2.503 0 0 0-2.292 1.5H17.25a.75.75 0 0 1 0 1.5H2.76a.75.75 0 0 1-.748-.807 4.002 4.002 0 0 1 2.716-3.486L3.626 2.716a.25.25 0 0 0-.248-.216H1.75A.75.75 0 0 1 1 1.75ZM6 17.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM15.5 19a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"
  }));
}
var ForwardRef282 = React282.forwardRef(ShoppingCartIcon);
var ShoppingCartIcon_default = ForwardRef282;

// node_modules/@heroicons/react/20/solid/esm/SignalSlashIcon.js
var React283 = __toESM(require_react(), 1);
function SignalSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React283.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React283.createElement("title", {
    id: titleId
  }, title) : null, React283.createElement("path", {
    d: "M2.22 2.22a.75.75 0 0 1 1.06 0l6.783 6.782a1 1 0 0 1 .935.935l6.782 6.783a.75.75 0 1 1-1.06 1.06l-6.783-6.782a1 1 0 0 1-.935-.935L2.22 3.28a.75.75 0 0 1 0-1.06ZM3.636 16.364a9.004 9.004 0 0 1-1.39-10.936L3.349 6.53a7.503 7.503 0 0 0 1.348 8.773.75.75 0 0 1-1.061 1.061ZM6.464 13.536a5 5 0 0 1-1.213-5.103l1.262 1.262a3.493 3.493 0 0 0 1.012 2.78.75.75 0 0 1-1.06 1.06ZM16.364 3.636a9.004 9.004 0 0 1 1.39 10.937l-1.103-1.104a7.503 7.503 0 0 0-1.348-8.772.75.75 0 1 1 1.061-1.061ZM13.536 6.464a5 5 0 0 1 1.213 5.103l-1.262-1.262a3.493 3.493 0 0 0-1.012-2.78.75.75 0 0 1 1.06-1.06Z"
  }));
}
var ForwardRef283 = React283.forwardRef(SignalSlashIcon);
var SignalSlashIcon_default = ForwardRef283;

// node_modules/@heroicons/react/20/solid/esm/SignalIcon.js
var React284 = __toESM(require_react(), 1);
function SignalIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React284.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React284.createElement("title", {
    id: titleId
  }, title) : null, React284.createElement("path", {
    d: "M16.364 3.636a.75.75 0 0 0-1.06 1.06 7.5 7.5 0 0 1 0 10.607.75.75 0 0 0 1.06 1.061 9 9 0 0 0 0-12.728ZM4.697 4.697a.75.75 0 0 0-1.061-1.061 9 9 0 0 0 0 12.728.75.75 0 1 0 1.06-1.06 7.5 7.5 0 0 1 0-10.607Z"
  }), React284.createElement("path", {
    d: "M12.475 6.464a.75.75 0 0 1 1.06 0 5 5 0 0 1 0 7.072.75.75 0 0 1-1.06-1.061 3.5 3.5 0 0 0 0-********** 0 0 1 0-1.06ZM7.525 6.464a.75.75 0 0 1 0 1.061 3.5 3.5 0 0 0 0 ********** 0 0 1-1.06 1.06 5 5 0 0 1 0-7.07.75.75 0 0 1 1.06 0ZM11 10a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"
  }));
}
var ForwardRef284 = React284.forwardRef(SignalIcon);
var SignalIcon_default = ForwardRef284;

// node_modules/@heroicons/react/20/solid/esm/SlashIcon.js
var React285 = __toESM(require_react(), 1);
function SlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React285.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React285.createElement("title", {
    id: titleId
  }, title) : null, React285.createElement("path", {
    fillRule: "evenodd",
    d: "M12.528 3.047a.75.75 0 0 1 .449.961L8.433 16.504a.75.75 0 1 1-1.41-.512l4.544-12.496a.75.75 0 0 1 .961-.449Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef285 = React285.forwardRef(SlashIcon);
var SlashIcon_default = ForwardRef285;

// node_modules/@heroicons/react/20/solid/esm/SparklesIcon.js
var React286 = __toESM(require_react(), 1);
function SparklesIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React286.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React286.createElement("title", {
    id: titleId
  }, title) : null, React286.createElement("path", {
    d: "M15.98 1.804a1 1 0 0 0-1.96 0l-.24 1.192a1 1 0 0 1-.784.785l-1.192.238a1 1 0 0 0 0 1.962l1.192.238a1 1 0 0 1 .785.785l.238 1.192a1 1 0 0 0 1.962 0l.238-1.192a1 1 0 0 1 .785-.785l1.192-.238a1 1 0 0 0 0-1.962l-1.192-.238a1 1 0 0 1-.785-.785l-.238-1.192ZM6.949 5.684a1 1 0 0 0-1.898 0l-.683 2.051a1 1 0 0 1-.633.633l-2.051.683a1 1 0 0 0 0 1.898l2.051.684a1 1 0 0 1 .633.632l.683 2.051a1 1 0 0 0 1.898 0l.683-2.051a1 1 0 0 1 .633-.633l2.051-.683a1 1 0 0 0 0-1.898l-2.051-.683a1 1 0 0 1-.633-.633L6.95 5.684ZM13.949 13.684a1 1 0 0 0-1.898 0l-.184.551a1 1 0 0 1-.632.633l-.551.183a1 1 0 0 0 0 1.898l.551.183a1 1 0 0 1 .633.633l.183.551a1 1 0 0 0 1.898 0l.184-.551a1 1 0 0 1 .632-.633l.551-.183a1 1 0 0 0 0-1.898l-.551-.184a1 1 0 0 1-.633-.632l-.183-.551Z"
  }));
}
var ForwardRef286 = React286.forwardRef(SparklesIcon);
var SparklesIcon_default = ForwardRef286;

// node_modules/@heroicons/react/20/solid/esm/SpeakerWaveIcon.js
var React287 = __toESM(require_react(), 1);
function SpeakerWaveIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React287.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React287.createElement("title", {
    id: titleId
  }, title) : null, React287.createElement("path", {
    d: "M10.5 3.75a.75.75 0 0 0-1.264-.546L5.203 7H2.667a.75.75 0 0 0-.7.48A6.985 6.985 0 0 0 1.5 10c0 .887.165 1.737.468 2.52.111.29.39.48.7.48h2.535l4.033 3.796a.75.75 0 0 0 1.264-.546V3.75ZM16.45 5.05a.75.75 0 0 0-1.06 1.061 5.5 5.5 0 0 1 0 7.778.75.75 0 0 0 1.06 1.06 7 7 0 0 0 0-9.899Z"
  }), React287.createElement("path", {
    d: "M14.329 7.172a.75.75 0 0 0-1.061 1.06 2.5 2.5 0 0 1 0 3.536.75.75 0 0 0 1.06 1.06 4 4 0 0 0 0-5.656Z"
  }));
}
var ForwardRef287 = React287.forwardRef(SpeakerWaveIcon);
var SpeakerWaveIcon_default = ForwardRef287;

// node_modules/@heroicons/react/20/solid/esm/SpeakerXMarkIcon.js
var React288 = __toESM(require_react(), 1);
function SpeakerXMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React288.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React288.createElement("title", {
    id: titleId
  }, title) : null, React288.createElement("path", {
    d: "M10.047 3.062a.75.75 0 0 1 .453.688v12.5a.75.75 0 0 1-1.264.546L5.203 13H2.667a.75.75 0 0 1-.7-.48A6.985 6.985 0 0 1 1.5 10c0-.887.165-1.737.468-2.52a.75.75 0 0 1 .7-.48h2.535l4.033-3.796a.75.75 0 0 1 .811-.142ZM13.78 7.22a.75.75 0 1 0-1.06 1.06L14.44 10l-1.72 1.72a.75.75 0 0 0 1.06 1.06l1.72-1.72 1.72 1.72a.75.75 0 1 0 1.06-1.06L16.56 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L15.5 8.94l-1.72-1.72Z"
  }));
}
var ForwardRef288 = React288.forwardRef(SpeakerXMarkIcon);
var SpeakerXMarkIcon_default = ForwardRef288;

// node_modules/@heroicons/react/20/solid/esm/Square2StackIcon.js
var React289 = __toESM(require_react(), 1);
function Square2StackIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React289.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React289.createElement("title", {
    id: titleId
  }, title) : null, React289.createElement("path", {
    d: "M2 4.25A2.25 2.25 0 0 1 4.25 2h6.5A2.25 2.25 0 0 1 13 4.25V5.5H9.25A3.75 3.75 0 0 0 5.5 9.25V13H4.25A2.25 2.25 0 0 1 2 10.75v-6.5Z"
  }), React289.createElement("path", {
    d: "M9.25 7A2.25 2.25 0 0 0 7 9.25v6.5A2.25 2.25 0 0 0 9.25 18h6.5A2.25 2.25 0 0 0 18 15.75v-6.5A2.25 2.25 0 0 0 15.75 7h-6.5Z"
  }));
}
var ForwardRef289 = React289.forwardRef(Square2StackIcon);
var Square2StackIcon_default = ForwardRef289;

// node_modules/@heroicons/react/20/solid/esm/Square3Stack3DIcon.js
var React290 = __toESM(require_react(), 1);
function Square3Stack3DIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React290.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React290.createElement("title", {
    id: titleId
  }, title) : null, React290.createElement("path", {
    d: "m3.196 12.87-.825.483a.75.75 0 0 0 0 1.294l7.25 4.25a.75.75 0 0 0 .758 0l7.25-4.25a.75.75 0 0 0 0-1.294l-.825-.484-5.666 3.322a2.25 2.25 0 0 1-2.276 0L3.196 12.87Z"
  }), React290.createElement("path", {
    d: "m3.196 8.87-.825.483a.75.75 0 0 0 0 1.294l7.25 4.25a.75.75 0 0 0 .758 0l7.25-4.25a.75.75 0 0 0 0-1.294l-.825-.484-5.666 3.322a2.25 2.25 0 0 1-2.276 0L3.196 8.87Z"
  }), React290.createElement("path", {
    d: "M10.38 1.103a.75.75 0 0 0-.76 0l-7.25 4.25a.75.75 0 0 0 0 1.294l7.25 4.25a.75.75 0 0 0 .76 0l7.25-4.25a.75.75 0 0 0 0-1.294l-7.25-4.25Z"
  }));
}
var ForwardRef290 = React290.forwardRef(Square3Stack3DIcon);
var Square3Stack3DIcon_default = ForwardRef290;

// node_modules/@heroicons/react/20/solid/esm/Squares2X2Icon.js
var React291 = __toESM(require_react(), 1);
function Squares2X2Icon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React291.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React291.createElement("title", {
    id: titleId
  }, title) : null, React291.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 2A2.25 2.25 0 0 0 2 4.25v2.5A2.25 2.25 0 0 0 4.25 9h2.5A2.25 2.25 0 0 0 9 6.75v-2.5A2.25 2.25 0 0 0 6.75 2h-2.5Zm0 9A2.25 2.25 0 0 0 2 13.25v2.5A2.25 2.25 0 0 0 4.25 18h2.5A2.25 2.25 0 0 0 9 15.75v-2.5A2.25 2.25 0 0 0 6.75 11h-2.5Zm9-9A2.25 2.25 0 0 0 11 4.25v2.5A2.25 2.25 0 0 0 13.25 9h2.5A2.25 2.25 0 0 0 18 6.75v-2.5A2.25 2.25 0 0 0 15.75 2h-2.5Zm0 9A2.25 2.25 0 0 0 11 13.25v2.5A2.25 2.25 0 0 0 13.25 18h2.5A2.25 2.25 0 0 0 18 15.75v-2.5A2.25 2.25 0 0 0 15.75 11h-2.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef291 = React291.forwardRef(Squares2X2Icon);
var Squares2X2Icon_default = ForwardRef291;

// node_modules/@heroicons/react/20/solid/esm/SquaresPlusIcon.js
var React292 = __toESM(require_react(), 1);
function SquaresPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React292.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React292.createElement("title", {
    id: titleId
  }, title) : null, React292.createElement("path", {
    d: "M2 4.25A2.25 2.25 0 0 1 4.25 2h2.5A2.25 2.25 0 0 1 9 4.25v2.5A2.25 2.25 0 0 1 6.75 9h-2.5A2.25 2.25 0 0 1 2 6.75v-2.5ZM2 13.25A2.25 2.25 0 0 1 4.25 11h2.5A2.25 2.25 0 0 1 9 13.25v2.5A2.25 2.25 0 0 1 6.75 18h-2.5A2.25 2.25 0 0 1 2 15.75v-2.5ZM11 4.25A2.25 2.25 0 0 1 13.25 2h2.5A2.25 2.25 0 0 1 18 4.25v2.5A2.25 2.25 0 0 1 15.75 9h-2.5A2.25 2.25 0 0 1 11 6.75v-2.5ZM15.25 11.75a.75.75 0 0 0-1.5 0v2h-2a.75.75 0 0 0 0 1.5h2v2a.75.75 0 0 0 1.5 0v-2h2a.75.75 0 0 0 0-1.5h-2v-2Z"
  }));
}
var ForwardRef292 = React292.forwardRef(SquaresPlusIcon);
var SquaresPlusIcon_default = ForwardRef292;

// node_modules/@heroicons/react/20/solid/esm/StarIcon.js
var React293 = __toESM(require_react(), 1);
function StarIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React293.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React293.createElement("title", {
    id: titleId
  }, title) : null, React293.createElement("path", {
    fillRule: "evenodd",
    d: "M10.868 2.884c-.321-.772-1.415-.772-1.736 0l-1.83 4.401-4.753.381c-.833.067-1.171 1.107-.536 1.651l3.62 3.102-1.106 4.637c-.194.813.691 1.456 1.405 1.02L10 15.591l4.069 2.485c.713.436 1.598-.207 1.404-1.02l-1.106-4.637 3.62-3.102c.635-.544.297-1.584-.536-1.65l-4.752-.382-1.831-4.401Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef293 = React293.forwardRef(StarIcon);
var StarIcon_default = ForwardRef293;

// node_modules/@heroicons/react/20/solid/esm/StopCircleIcon.js
var React294 = __toESM(require_react(), 1);
function StopCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React294.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React294.createElement("title", {
    id: titleId
  }, title) : null, React294.createElement("path", {
    fillRule: "evenodd",
    d: "M2 10a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm5-2.25A.75.75 0 0 1 7.75 7h4.5a.75.75 0 0 1 .75.75v4.5a.75.75 0 0 1-.75.75h-4.5a.75.75 0 0 1-.75-.75v-4.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef294 = React294.forwardRef(StopCircleIcon);
var StopCircleIcon_default = ForwardRef294;

// node_modules/@heroicons/react/20/solid/esm/StopIcon.js
var React295 = __toESM(require_react(), 1);
function StopIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React295.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React295.createElement("title", {
    id: titleId
  }, title) : null, React295.createElement("path", {
    d: "M5.25 3A2.25 2.25 0 0 0 3 5.25v9.5A2.25 2.25 0 0 0 5.25 17h9.5A2.25 2.25 0 0 0 17 14.75v-9.5A2.25 2.25 0 0 0 14.75 3h-9.5Z"
  }));
}
var ForwardRef295 = React295.forwardRef(StopIcon);
var StopIcon_default = ForwardRef295;

// node_modules/@heroicons/react/20/solid/esm/StrikethroughIcon.js
var React296 = __toESM(require_react(), 1);
function StrikethroughIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React296.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React296.createElement("title", {
    id: titleId
  }, title) : null, React296.createElement("path", {
    fillRule: "evenodd",
    d: "M11.617 3.963c-1.186-.318-2.418-.323-3.416.015-.992.336-1.49.91-1.642 1.476-.152.566-.007 1.313.684 2.1.528.6 1.273 1.1 2.128 1.446h7.879a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1 0-1.5h3.813a5.976 5.976 0 0 1-.447-.456C5.18 7.479 4.798 6.231 5.11 5.066c.312-1.164 1.268-2.055 2.61-2.509 1.336-.451 2.877-.42 4.286-.043.856.23 1.684.592 2.409 1.074a.75.75 0 1 1-.83 1.25 6.723 6.723 0 0 0-1.968-.875Zm1.909 8.123a.75.75 0 0 1 1.015.309c.53.99.607 2.062.18 3.01-.421.94-1.289 1.648-2.441 2.038-1.336.452-2.877.42-4.286.043-1.409-.377-2.759-1.121-3.69-2.18a.75.75 0 1 1 1.127-.99c.696.791 1.765 1.403 2.952 1.721 1.186.318 2.418.323 3.416-.015.853-.288 1.34-.756 1.555-1.232.21-.467.205-1.049-.136-1.69a.75.75 0 0 1 .308-1.014Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef296 = React296.forwardRef(StrikethroughIcon);
var StrikethroughIcon_default = ForwardRef296;

// node_modules/@heroicons/react/20/solid/esm/SunIcon.js
var React297 = __toESM(require_react(), 1);
function SunIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React297.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React297.createElement("title", {
    id: titleId
  }, title) : null, React297.createElement("path", {
    d: "M10 2a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 10 2ZM10 15a.75.75 0 0 1 .75.75v1.5a.75.75 0 0 1-1.5 0v-1.5A.75.75 0 0 1 10 15ZM10 7a3 3 0 1 0 0 6 3 3 0 0 0 0-6ZM15.657 5.404a.75.75 0 1 0-1.06-1.06l-1.061 1.06a.75.75 0 0 0 1.06 1.06l1.06-1.06ZM6.464 14.596a.75.75 0 1 0-1.06-1.06l-1.06 1.06a.75.75 0 0 0 1.06 1.06l1.06-1.06ZM18 10a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 18 10ZM5 10a.75.75 0 0 1-.75.75h-1.5a.75.75 0 0 1 0-1.5h1.5A.75.75 0 0 1 5 10ZM14.596 15.657a.75.75 0 0 0 1.06-1.06l-1.06-1.061a.75.75 0 1 0-1.06 1.06l1.06 1.06ZM5.404 6.464a.75.75 0 0 0 1.06-1.06l-1.06-1.06a.75.75 0 1 0-1.061 1.06l1.06 1.06Z"
  }));
}
var ForwardRef297 = React297.forwardRef(SunIcon);
var SunIcon_default = ForwardRef297;

// node_modules/@heroicons/react/20/solid/esm/SwatchIcon.js
var React298 = __toESM(require_react(), 1);
function SwatchIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React298.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React298.createElement("title", {
    id: titleId
  }, title) : null, React298.createElement("path", {
    fillRule: "evenodd",
    d: "M3.5 2A1.5 1.5 0 0 0 2 3.5V15a3 3 0 1 0 6 0V3.5A1.5 1.5 0 0 0 6.5 2h-3Zm11.753 6.99L9.5 14.743V6.257l1.51-1.51a1.5 1.5 0 0 1 2.122 0l2.121 2.121a1.5 1.5 0 0 1 0 2.122ZM8.364 18H16.5a1.5 1.5 0 0 0 1.5-1.5v-3a1.5 1.5 0 0 0-1.5-1.5h-2.136l-6 6ZM5 16a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef298 = React298.forwardRef(SwatchIcon);
var SwatchIcon_default = ForwardRef298;

// node_modules/@heroicons/react/20/solid/esm/TableCellsIcon.js
var React299 = __toESM(require_react(), 1);
function TableCellsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React299.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React299.createElement("title", {
    id: titleId
  }, title) : null, React299.createElement("path", {
    fillRule: "evenodd",
    d: "M.99 5.24A2.25 2.25 0 0 1 3.25 3h13.5A2.25 2.25 0 0 1 19 5.25l.01 9.5A2.25 2.25 0 0 1 16.76 17H3.26A2.267 2.267 0 0 1 1 14.74l-.01-9.5Zm8.26 9.52v-.625a.75.75 0 0 0-.75-.75H3.25a.75.75 0 0 0-.75.75v.615c0 .414.336.75.75.75h5.373a.75.75 0 0 0 .627-.74Zm1.5 0a.75.75 0 0 0 .627.74h5.373a.75.75 0 0 0 .75-.75v-.615a.75.75 0 0 0-.75-.75H11.5a.75.75 0 0 0-.75.75v.625Zm6.75-3.63v-.625a.75.75 0 0 0-.75-.75H11.5a.75.75 0 0 0-.75.75v.625c0 .414.336.75.75.75h5.25a.75.75 0 0 0 .75-.75Zm-8.25 0v-.625a.75.75 0 0 0-.75-.75H3.25a.75.75 0 0 0-.75.75v.625c0 .414.336.75.75.75H8.5a.75.75 0 0 0 .75-.75ZM17.5 7.5v-.625a.75.75 0 0 0-.75-.75H11.5a.75.75 0 0 0-.75.75V7.5c0 .414.336.75.75.75h5.25a.75.75 0 0 0 .75-.75Zm-8.25 0v-.625a.75.75 0 0 0-.75-.75H3.25a.75.75 0 0 0-.75.75V7.5c0 .414.336.75.75.75H8.5a.75.75 0 0 0 .75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef299 = React299.forwardRef(TableCellsIcon);
var TableCellsIcon_default = ForwardRef299;

// node_modules/@heroicons/react/20/solid/esm/TagIcon.js
var React300 = __toESM(require_react(), 1);
function TagIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React300.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React300.createElement("title", {
    id: titleId
  }, title) : null, React300.createElement("path", {
    fillRule: "evenodd",
    d: "M4.5 2A2.5 2.5 0 0 0 2 4.5v3.879a2.5 2.5 0 0 0 .732 1.767l7.5 7.5a2.5 2.5 0 0 0 3.536 0l3.878-3.878a2.5 2.5 0 0 0 0-3.536l-7.5-7.5A2.5 2.5 0 0 0 8.38 2H4.5ZM5 6a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef300 = React300.forwardRef(TagIcon);
var TagIcon_default = ForwardRef300;

// node_modules/@heroicons/react/20/solid/esm/TicketIcon.js
var React301 = __toESM(require_react(), 1);
function TicketIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React301.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React301.createElement("title", {
    id: titleId
  }, title) : null, React301.createElement("path", {
    fillRule: "evenodd",
    d: "M15.75 3A2.25 2.25 0 0 1 18 5.25v1.214c0 .423-.277.788-.633 1.019A2.997 2.997 0 0 0 16 10c0 1.055.544 1.982 1.367 2.517.356.231.633.596.633 1.02v1.213A2.25 2.25 0 0 1 15.75 17H4.25A2.25 2.25 0 0 1 2 14.75v-1.213c0-.424.277-.789.633-1.02A2.998 2.998 0 0 0 4 10a2.997 2.997 0 0 0-1.367-2.517C2.277 7.252 2 6.887 2 6.463V5.25A2.25 2.25 0 0 1 4.25 3h11.5ZM13.5 7.396a.75.75 0 0 0-1.5 0v1.042a.75.75 0 0 0 1.5 0V7.396Zm0 4.167a.75.75 0 0 0-1.5 0v1.041a.75.75 0 0 0 1.5 0v-1.041Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef301 = React301.forwardRef(TicketIcon);
var TicketIcon_default = ForwardRef301;

// node_modules/@heroicons/react/20/solid/esm/TrashIcon.js
var React302 = __toESM(require_react(), 1);
function TrashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React302.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React302.createElement("title", {
    id: titleId
  }, title) : null, React302.createElement("path", {
    fillRule: "evenodd",
    d: "M8.75 1A2.75 2.75 0 0 0 6 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 1 0 .23 1.482l.149-.022.841 10.518A2.75 2.75 0 0 0 7.596 19h4.807a2.75 2.75 0 0 0 2.742-2.53l.841-10.52.149.023a.75.75 0 0 0 .23-1.482A41.03 41.03 0 0 0 14 4.193V3.75A2.75 2.75 0 0 0 11.25 1h-2.5ZM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4ZM8.58 7.72a.75.75 0 0 0-1.5.06l.3 7.5a.75.75 0 1 0 1.5-.06l-.3-7.5Zm4.34.06a.75.75 0 1 0-1.5-.06l-.3 7.5a.75.75 0 1 0 1.5.06l.3-7.5Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef302 = React302.forwardRef(TrashIcon);
var TrashIcon_default = ForwardRef302;

// node_modules/@heroicons/react/20/solid/esm/TrophyIcon.js
var React303 = __toESM(require_react(), 1);
function TrophyIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React303.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React303.createElement("title", {
    id: titleId
  }, title) : null, React303.createElement("path", {
    fillRule: "evenodd",
    d: "M10 1c-1.828 0-3.623.149-5.371.435a.75.75 0 0 0-.629.74v.387c-.827.157-1.642.345-2.445.564a.75.75 0 0 0-.552.698 5 5 0 0 0 4.503 5.152 6 6 0 0 0 2.946 1.822A6.451 6.451 0 0 1 7.768 13H7.5A1.5 1.5 0 0 0 6 14.5V17h-.75C4.56 17 4 17.56 4 18.25c0 .414.336.75.75.75h10.5a.75.75 0 0 0 .75-.75c0-.69-.56-1.25-1.25-1.25H14v-2.5a1.5 1.5 0 0 0-1.5-1.5h-.268a6.453 6.453 0 0 1-.684-2.202 6 6 0 0 0 2.946-1.822 5 5 0 0 0 4.503-*********** 0 0 0-.552-.698A31.804 31.804 0 0 0 16 2.562v-.387a.75.75 0 0 0-.629-.74A33.227 33.227 0 0 0 10 1ZM2.525 4.422C3.012 4.3 3.504 4.19 4 4.09V5c0 .74.134 1.448.38 2.103a3.503 3.503 0 0 1-1.855-2.68Zm14.95 0a3.503 3.503 0 0 1-1.854 2.68C15.866 6.449 16 5.74 16 5v-.91c.496.099.988.21 1.475.332Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef303 = React303.forwardRef(TrophyIcon);
var TrophyIcon_default = ForwardRef303;

// node_modules/@heroicons/react/20/solid/esm/TruckIcon.js
var React304 = __toESM(require_react(), 1);
function TruckIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React304.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React304.createElement("title", {
    id: titleId
  }, title) : null, React304.createElement("path", {
    d: "M6.5 3c-1.051 0-2.093.04-3.125.117A1.49 1.49 0 0 0 2 4.607V10.5h9V4.606c0-.771-.59-1.43-1.375-1.489A41.568 41.568 0 0 0 6.5 3ZM2 12v2.5A1.5 1.5 0 0 0 3.5 16h.041a3 3 0 0 1 5.918 0h.791a.75.75 0 0 0 .75-.75V12H2Z"
  }), React304.createElement("path", {
    d: "M6.5 18a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3ZM13.25 5a.75.75 0 0 0-.75.75v8.514a3.001 3.001 0 0 1 4.893 1.44c.37-.275.61-.719.595-1.227a24.905 24.905 0 0 0-1.784-8.549A1.486 1.486 0 0 0 14.823 5H13.25ZM14.5 18a1.5 1.5 0 1 0 0-3 1.5 1.5 0 0 0 0 3Z"
  }));
}
var ForwardRef304 = React304.forwardRef(TruckIcon);
var TruckIcon_default = ForwardRef304;

// node_modules/@heroicons/react/20/solid/esm/TvIcon.js
var React305 = __toESM(require_react(), 1);
function TvIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React305.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React305.createElement("title", {
    id: titleId
  }, title) : null, React305.createElement("path", {
    d: "M4 5h12v7H4V5Z"
  }), React305.createElement("path", {
    fillRule: "evenodd",
    d: "M1 3.5A1.5 1.5 0 0 1 2.5 2h15A1.5 1.5 0 0 1 19 3.5v10a1.5 1.5 0 0 1-1.5 1.5H12v1.5h3.25a.75.75 0 0 1 0 1.5H4.75a.75.75 0 0 1 0-1.5H8V15H2.5A1.5 1.5 0 0 1 1 13.5v-10Zm16.5 0h-15v10h15v-10Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef305 = React305.forwardRef(TvIcon);
var TvIcon_default = ForwardRef305;

// node_modules/@heroicons/react/20/solid/esm/UnderlineIcon.js
var React306 = __toESM(require_react(), 1);
function UnderlineIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React306.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React306.createElement("title", {
    id: titleId
  }, title) : null, React306.createElement("path", {
    fillRule: "evenodd",
    d: "M4.75 2a.75.75 0 0 1 .75.75V9a4.5 4.5 0 1 0 9 0V2.75a.75.75 0 0 1 1.5 0V9A6 6 0 0 1 4 9V2.75A.75.75 0 0 1 4.75 2ZM2 17.25a.75.75 0 0 1 .75-.75h14.5a.75.75 0 0 1 0 1.5H2.75a.75.75 0 0 1-.75-.75Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef306 = React306.forwardRef(UnderlineIcon);
var UnderlineIcon_default = ForwardRef306;

// node_modules/@heroicons/react/20/solid/esm/UserCircleIcon.js
var React307 = __toESM(require_react(), 1);
function UserCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React307.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React307.createElement("title", {
    id: titleId
  }, title) : null, React307.createElement("path", {
    fillRule: "evenodd",
    d: "M18 10a8 8 0 1 1-16 0 8 8 0 0 1 16 0Zm-5.5-2.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0ZM10 12a5.99 5.99 0 0 0-4.793 2.39A6.483 6.483 0 0 0 10 16.5a6.483 6.483 0 0 0 4.793-2.11A5.99 5.99 0 0 0 10 12Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef307 = React307.forwardRef(UserCircleIcon);
var UserCircleIcon_default = ForwardRef307;

// node_modules/@heroicons/react/20/solid/esm/UserGroupIcon.js
var React308 = __toESM(require_react(), 1);
function UserGroupIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React308.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React308.createElement("title", {
    id: titleId
  }, title) : null, React308.createElement("path", {
    d: "M10 9a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM6 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0ZM1.49 15.326a.78.78 0 0 1-.358-.442 3 3 0 0 1 4.308-3.516 6.484 6.484 0 0 0-1.905 3.959c-.023.222-.014.442.025.654a4.97 4.97 0 0 1-2.07-.655ZM16.44 15.98a4.97 4.97 0 0 0 2.07-.654.78.78 0 0 0 .357-.442 3 3 0 0 0-4.308-3.517 6.484 6.484 0 0 1 1.907 3.96 2.32 2.32 0 0 1-.026.654ZM18 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0ZM5.304 16.19a.844.844 0 0 1-.277-.71 5 5 0 0 1 9.947 0 .843.843 0 0 1-.277.71A6.975 6.975 0 0 1 10 18a6.974 6.974 0 0 1-4.696-1.81Z"
  }));
}
var ForwardRef308 = React308.forwardRef(UserGroupIcon);
var UserGroupIcon_default = ForwardRef308;

// node_modules/@heroicons/react/20/solid/esm/UserMinusIcon.js
var React309 = __toESM(require_react(), 1);
function UserMinusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React309.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React309.createElement("title", {
    id: titleId
  }, title) : null, React309.createElement("path", {
    d: "M11 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM2.046 15.253c-.058.468.172.92.57 1.175A9.953 9.953 0 0 0 8 18c1.982 0 3.83-.578 5.384-1.573.398-.254.628-.707.57-1.175a6.001 6.001 0 0 0-11.908 0ZM12.75 7.75a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5Z"
  }));
}
var ForwardRef309 = React309.forwardRef(UserMinusIcon);
var UserMinusIcon_default = ForwardRef309;

// node_modules/@heroicons/react/20/solid/esm/UserPlusIcon.js
var React310 = __toESM(require_react(), 1);
function UserPlusIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React310.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React310.createElement("title", {
    id: titleId
  }, title) : null, React310.createElement("path", {
    d: "M10 5a3 3 0 1 1-6 0 3 3 0 0 1 6 0ZM1.615 16.428a1.224 1.224 0 0 1-.569-1.175 6.002 6.002 0 0 1 11.908 0c.058.467-.172.92-.57 1.174A9.953 9.953 0 0 1 7 18a9.953 9.953 0 0 1-5.385-1.572ZM16.25 5.75a.75.75 0 0 0-1.5 0v2h-2a.75.75 0 0 0 0 1.5h2v2a.75.75 0 0 0 1.5 0v-2h2a.75.75 0 0 0 0-1.5h-2v-2Z"
  }));
}
var ForwardRef310 = React310.forwardRef(UserPlusIcon);
var UserPlusIcon_default = ForwardRef310;

// node_modules/@heroicons/react/20/solid/esm/UserIcon.js
var React311 = __toESM(require_react(), 1);
function UserIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React311.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React311.createElement("title", {
    id: titleId
  }, title) : null, React311.createElement("path", {
    d: "M10 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM3.465 14.493a1.23 1.23 0 0 0 .41 1.412A9.957 9.957 0 0 0 10 18c2.31 0 4.438-.784 6.131-2.1.43-.333.604-.903.408-1.41a7.002 7.002 0 0 0-13.074.003Z"
  }));
}
var ForwardRef311 = React311.forwardRef(UserIcon);
var UserIcon_default = ForwardRef311;

// node_modules/@heroicons/react/20/solid/esm/UsersIcon.js
var React312 = __toESM(require_react(), 1);
function UsersIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React312.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React312.createElement("title", {
    id: titleId
  }, title) : null, React312.createElement("path", {
    d: "M7 8a3 3 0 1 0 0-6 3 3 0 0 0 0 6ZM14.5 9a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM1.615 16.428a1.224 1.224 0 0 1-.569-1.175 6.002 6.002 0 0 1 11.908 0c.058.467-.172.92-.57 1.174A9.953 9.953 0 0 1 7 18a9.953 9.953 0 0 1-5.385-1.572ZM14.5 16h-.106c.07-.297.088-.611.048-.933a7.47 7.47 0 0 0-1.588-3.755 4.502 4.502 0 0 1 5.874 2.636.818.818 0 0 1-.36.98A7.465 7.465 0 0 1 14.5 16Z"
  }));
}
var ForwardRef312 = React312.forwardRef(UsersIcon);
var UsersIcon_default = ForwardRef312;

// node_modules/@heroicons/react/20/solid/esm/VariableIcon.js
var React313 = __toESM(require_react(), 1);
function VariableIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React313.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React313.createElement("title", {
    id: titleId
  }, title) : null, React313.createElement("path", {
    fillRule: "evenodd",
    d: "M15.212 2.079a.75.75 0 0 1 1.006.336A16.932 16.932 0 0 1 18 10c0 2.724-.641 5.3-1.782 7.585a.75.75 0 1 1-1.342-.67A15.432 15.432 0 0 0 16.5 10c0-2.486-.585-4.834-1.624-6.915a.75.75 0 0 1 .336-1.006Zm-10.424 0a.75.75 0 0 1 .336 1.006A15.433 15.433 0 0 0 3.5 10c0 2.486.585 4.834 1.624 6.915a.75.75 0 1 1-1.342.67A16.933 16.933 0 0 1 2 10c0-2.724.641-5.3 1.782-7.585a.75.75 0 0 1 1.006-.336Zm2.285 3.554a1.5 1.5 0 0 1 2.219.677l.856 2.08 1.146-1.77a2.25 2.25 0 0 1 3.137-.65l.235.156a.75.75 0 1 1-.832 1.248l-.235-.156a.75.75 0 0 0-1.045.216l-1.71 2.644 1.251 3.04.739-.492a.75.75 0 1 1 .832 1.248l-.739.493a1.5 1.5 0 0 1-2.219-.677l-.856-2.08-1.146 1.77a2.25 2.25 0 0 1-3.137.65l-.235-.156a.75.75 0 0 1 .832-1.248l.235.157a.75.75 0 0 0 1.045-.217l1.71-2.644-1.251-3.04-.739.492a.75.75 0 0 1-.832-1.248l.739-.493Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef313 = React313.forwardRef(VariableIcon);
var VariableIcon_default = ForwardRef313;

// node_modules/@heroicons/react/20/solid/esm/VideoCameraSlashIcon.js
var React314 = __toESM(require_react(), 1);
function VideoCameraSlashIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React314.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React314.createElement("title", {
    id: titleId
  }, title) : null, React314.createElement("path", {
    d: "M1 13.75V7.182L9.818 16H3.25A2.25 2.25 0 0 1 1 13.75ZM13 6.25v6.568L4.182 4h6.568A2.25 2.25 0 0 1 13 6.25ZM19 4.75a.75.75 0 0 0-1.28-.53l-3 3a.75.75 0 0 0-.22.53v4.5c0 .*************.53l3 3a.75.75 0 0 0 1.28-.53V4.75ZM2.28 4.22a.75.75 0 0 0-1.06 1.06l10.5 10.5a.75.75 0 1 0 1.06-1.06L2.28 4.22Z"
  }));
}
var ForwardRef314 = React314.forwardRef(VideoCameraSlashIcon);
var VideoCameraSlashIcon_default = ForwardRef314;

// node_modules/@heroicons/react/20/solid/esm/VideoCameraIcon.js
var React315 = __toESM(require_react(), 1);
function VideoCameraIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React315.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React315.createElement("title", {
    id: titleId
  }, title) : null, React315.createElement("path", {
    d: "M3.25 4A2.25 2.25 0 0 0 1 6.25v7.5A2.25 2.25 0 0 0 3.25 16h7.5A2.25 2.25 0 0 0 13 13.75v-7.5A2.25 2.25 0 0 0 10.75 4h-7.5ZM19 4.75a.75.75 0 0 0-1.28-.53l-3 3a.75.75 0 0 0-.22.53v4.5c0 .*************.53l3 3a.75.75 0 0 0 1.28-.53V4.75Z"
  }));
}
var ForwardRef315 = React315.forwardRef(VideoCameraIcon);
var VideoCameraIcon_default = ForwardRef315;

// node_modules/@heroicons/react/20/solid/esm/ViewColumnsIcon.js
var React316 = __toESM(require_react(), 1);
function ViewColumnsIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React316.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React316.createElement("title", {
    id: titleId
  }, title) : null, React316.createElement("path", {
    d: "M14 17h2.75A2.25 2.25 0 0 0 19 14.75v-9.5A2.25 2.25 0 0 0 16.75 3H14v14ZM12.5 3h-5v14h5V3ZM3.25 3H6v14H3.25A2.25 2.25 0 0 1 1 14.75v-9.5A2.25 2.25 0 0 1 3.25 3Z"
  }));
}
var ForwardRef316 = React316.forwardRef(ViewColumnsIcon);
var ViewColumnsIcon_default = ForwardRef316;

// node_modules/@heroicons/react/20/solid/esm/ViewfinderCircleIcon.js
var React317 = __toESM(require_react(), 1);
function ViewfinderCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React317.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React317.createElement("title", {
    id: titleId
  }, title) : null, React317.createElement("path", {
    d: "M4.25 2A2.25 2.25 0 0 0 2 4.25v2a.75.75 0 0 0 1.5 0v-2a.75.75 0 0 1 .75-.75h2a.75.75 0 0 0 0-1.5h-2ZM13.75 2a.75.75 0 0 0 0 1.5h2a.75.75 0 0 1 .75.75v2a.75.75 0 0 0 1.5 0v-2A2.25 2.25 0 0 0 15.75 2h-2ZM3.5 13.75a.75.75 0 0 0-1.5 0v2A2.25 2.25 0 0 0 4.25 18h2a.75.75 0 0 0 0-1.5h-2a.75.75 0 0 1-.75-.75v-2ZM18 13.75a.75.75 0 0 0-1.5 0v2a.75.75 0 0 1-.75.75h-2a.75.75 0 0 0 0 1.5h2A2.25 2.25 0 0 0 18 15.75v-2ZM7 10a3 3 0 1 1 6 0 3 3 0 0 1-6 0Z"
  }));
}
var ForwardRef317 = React317.forwardRef(ViewfinderCircleIcon);
var ViewfinderCircleIcon_default = ForwardRef317;

// node_modules/@heroicons/react/20/solid/esm/WalletIcon.js
var React318 = __toESM(require_react(), 1);
function WalletIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React318.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React318.createElement("title", {
    id: titleId
  }, title) : null, React318.createElement("path", {
    d: "M1 4.25a3.733 3.733 0 0 1 2.25-.75h13.5c.844 0 1.623.279 2.25.75A2.25 2.25 0 0 0 16.75 2H3.25A2.25 2.25 0 0 0 1 4.25ZM1 7.25a3.733 3.733 0 0 1 2.25-.75h13.5c.844 0 1.623.279 2.25.75A2.25 2.25 0 0 0 16.75 5H3.25A2.25 2.25 0 0 0 1 7.25ZM7 8a1 1 0 0 1 1 1 2 2 0 1 0 4 0 1 1 0 0 1 1-1h3.75A2.25 2.25 0 0 1 19 10.25v5.5A2.25 2.25 0 0 1 16.75 18H3.25A2.25 2.25 0 0 1 1 15.75v-5.5A2.25 2.25 0 0 1 3.25 8H7Z"
  }));
}
var ForwardRef318 = React318.forwardRef(WalletIcon);
var WalletIcon_default = ForwardRef318;

// node_modules/@heroicons/react/20/solid/esm/WifiIcon.js
var React319 = __toESM(require_react(), 1);
function WifiIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React319.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React319.createElement("title", {
    id: titleId
  }, title) : null, React319.createElement("path", {
    fillRule: "evenodd",
    d: "M.676 6.941A12.964 12.964 0 0 1 10 3c3.657 0 6.963 1.511 9.324 3.941a.75.75 0 0 1-.008 1.053l-.353.354a.75.75 0 0 1-1.069-.008C15.894 6.28 13.097 5 10 5 6.903 5 4.106 6.28 2.106 8.34a.75.75 0 0 1-1.069.008l-.353-.354a.75.75 0 0 1-.008-1.053Zm2.825 2.833A8.976 8.976 0 0 1 10 7a8.976 8.976 0 0 1 6.499 2.774.75.75 0 0 1-.011 1.049l-.354.354a.75.75 0 0 1-1.072-.012A6.978 6.978 0 0 0 10 9c-1.99 0-3.786.83-5.061 2.165a.75.75 0 0 1-1.073.012l-.354-.354a.75.75 0 0 1-.01-1.05Zm2.82 2.84A4.989 4.989 0 0 1 10 11c1.456 0 2.767.623 3.68 1.614a.75.75 0 0 1-.022 1.039l-.354.354a.75.75 0 0 1-1.085-.026A2.99 2.99 0 0 0 10 13c-.88 0-1.67.377-2.22.981a.75.75 0 0 1-1.084.026l-.354-.354a.75.75 0 0 1-.021-1.039Zm2.795 2.752a1.248 1.248 0 0 1 1.768 0 .75.75 0 0 1 0 1.06l-.354.354a.75.75 0 0 1-1.06 0l-.354-.353a.75.75 0 0 1 0-1.06Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef319 = React319.forwardRef(WifiIcon);
var WifiIcon_default = ForwardRef319;

// node_modules/@heroicons/react/20/solid/esm/WindowIcon.js
var React320 = __toESM(require_react(), 1);
function WindowIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React320.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React320.createElement("title", {
    id: titleId
  }, title) : null, React320.createElement("path", {
    fillRule: "evenodd",
    d: "M4.25 2A2.25 2.25 0 0 0 2 4.25v11.5A2.25 2.25 0 0 0 4.25 18h11.5A2.25 2.25 0 0 0 18 15.75V4.25A2.25 2.25 0 0 0 15.75 2H4.25ZM3.5 8v7.75c0 .414.336.75.75.75h11.5a.75.75 0 0 0 .75-.75V8h-13ZM5 4.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V5a.75.75 0 0 0-.75-.75H5ZM7.25 5A.75.75 0 0 1 8 4.25h.01a.75.75 0 0 1 .75.75v.01a.75.75 0 0 1-.75.75H8a.75.75 0 0 1-.75-.75V5ZM11 4.25a.75.75 0 0 0-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 0 0 .75-.75V5a.75.75 0 0 0-.75-.75H11Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef320 = React320.forwardRef(WindowIcon);
var WindowIcon_default = ForwardRef320;

// node_modules/@heroicons/react/20/solid/esm/WrenchScrewdriverIcon.js
var React321 = __toESM(require_react(), 1);
function WrenchScrewdriverIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React321.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React321.createElement("title", {
    id: titleId
  }, title) : null, React321.createElement("path", {
    fillRule: "evenodd",
    d: "M14.5 10a4.5 4.5 0 0 0 4.284-5.882c-.105-.324-.51-.391-.752-.15L15.34 6.66a.454.454 0 0 1-.493.11 3.01 3.01 0 0 1-1.618-1.616.455.455 0 0 1 .11-.494l2.694-2.692c.24-.241.174-.647-.15-.752a4.5 4.5 0 0 0-5.873 4.575c.055.873-.128 1.808-.8 2.368l-7.23 6.024a2.724 2.724 0 1 0 3.837 3.837l6.024-7.23c.56-.672 1.495-.855 2.368-.*************.01.291.01ZM5 16a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z",
    clipRule: "evenodd"
  }), React321.createElement("path", {
    d: "M14.5 11.5c.173 0 .345-.007.514-.022l3.754 3.754a2.5 2.5 0 0 1-3.536 3.536l-4.41-4.41 2.172-2.607c.052-.063.147-.138.342-.196.202-.06.469-.087.777-.067.128.008.257.012.387.012ZM6 4.586l2.33 2.33a.452.452 0 0 1-.08.09L6.8 8.214 4.586 6H3.309a.5.5 0 0 1-.447-.276l-1.7-3.402a.5.5 0 0 1 .093-.577l.49-.49a.5.5 0 0 1 .577-.094l3.402 1.7A.5.5 0 0 1 6 3.31v1.277Z"
  }));
}
var ForwardRef321 = React321.forwardRef(WrenchScrewdriverIcon);
var WrenchScrewdriverIcon_default = ForwardRef321;

// node_modules/@heroicons/react/20/solid/esm/WrenchIcon.js
var React322 = __toESM(require_react(), 1);
function WrenchIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React322.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React322.createElement("title", {
    id: titleId
  }, title) : null, React322.createElement("path", {
    fillRule: "evenodd",
    d: "M19 5.5a4.5 4.5 0 0 1-4.791 4.49c-.873-.055-1.808.128-2.368.8l-6.024 7.23a2.724 2.724 0 1 1-3.837-3.837L9.21 8.16c.672-.56.855-1.495.8-2.368a4.5 4.5 0 0 1 5.873-4.575c.324.************.752L13.34 4.66a.455.455 0 0 0-.11.494 3.01 3.01 0 0 0 1.617 1.617c.17.07.363.02.493-.111l2.692-2.692c.241-.241.647-.174.752.15.14.435.216.9.216 1.382ZM4 17a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef322 = React322.forwardRef(WrenchIcon);
var WrenchIcon_default = ForwardRef322;

// node_modules/@heroicons/react/20/solid/esm/XCircleIcon.js
var React323 = __toESM(require_react(), 1);
function XCircleIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React323.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React323.createElement("title", {
    id: titleId
  }, title) : null, React323.createElement("path", {
    fillRule: "evenodd",
    d: "M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16ZM8.28 7.22a.75.75 0 0 0-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 1 0 1.06 1.06L10 11.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L11.06 10l1.72-1.72a.75.75 0 0 0-1.06-1.06L10 8.94 8.28 7.22Z",
    clipRule: "evenodd"
  }));
}
var ForwardRef323 = React323.forwardRef(XCircleIcon);
var XCircleIcon_default = ForwardRef323;

// node_modules/@heroicons/react/20/solid/esm/XMarkIcon.js
var React324 = __toESM(require_react(), 1);
function XMarkIcon({
  title,
  titleId,
  ...props
}, svgRef) {
  return React324.createElement("svg", Object.assign({
    xmlns: "http://www.w3.org/2000/svg",
    viewBox: "0 0 20 20",
    fill: "currentColor",
    "aria-hidden": "true",
    "data-slot": "icon",
    ref: svgRef,
    "aria-labelledby": titleId
  }, props), title ? React324.createElement("title", {
    id: titleId
  }, title) : null, React324.createElement("path", {
    d: "M6.28 5.22a.75.75 0 0 0-1.06 1.06L8.94 10l-3.72 3.72a.75.75 0 1 0 1.06 1.06L10 11.06l3.72 3.72a.75.75 0 1 0 1.06-1.06L11.06 10l3.72-3.72a.75.75 0 0 0-1.06-1.06L10 8.94 6.28 5.22Z"
  }));
}
var ForwardRef324 = React324.forwardRef(XMarkIcon);
var XMarkIcon_default = ForwardRef324;
export {
  AcademicCapIcon_default as AcademicCapIcon,
  AdjustmentsHorizontalIcon_default as AdjustmentsHorizontalIcon,
  AdjustmentsVerticalIcon_default as AdjustmentsVerticalIcon,
  ArchiveBoxArrowDownIcon_default as ArchiveBoxArrowDownIcon,
  ArchiveBoxIcon_default as ArchiveBoxIcon,
  ArchiveBoxXMarkIcon_default as ArchiveBoxXMarkIcon,
  ArrowDownCircleIcon_default as ArrowDownCircleIcon,
  ArrowDownIcon_default as ArrowDownIcon,
  ArrowDownLeftIcon_default as ArrowDownLeftIcon,
  ArrowDownOnSquareIcon_default as ArrowDownOnSquareIcon,
  ArrowDownOnSquareStackIcon_default as ArrowDownOnSquareStackIcon,
  ArrowDownRightIcon_default as ArrowDownRightIcon,
  ArrowDownTrayIcon_default as ArrowDownTrayIcon,
  ArrowLeftCircleIcon_default as ArrowLeftCircleIcon,
  ArrowLeftEndOnRectangleIcon_default as ArrowLeftEndOnRectangleIcon,
  ArrowLeftIcon_default as ArrowLeftIcon,
  ArrowLeftOnRectangleIcon_default as ArrowLeftOnRectangleIcon,
  ArrowLeftStartOnRectangleIcon_default as ArrowLeftStartOnRectangleIcon,
  ArrowLongDownIcon_default as ArrowLongDownIcon,
  ArrowLongLeftIcon_default as ArrowLongLeftIcon,
  ArrowLongRightIcon_default as ArrowLongRightIcon,
  ArrowLongUpIcon_default as ArrowLongUpIcon,
  ArrowPathIcon_default as ArrowPathIcon,
  ArrowPathRoundedSquareIcon_default as ArrowPathRoundedSquareIcon,
  ArrowRightCircleIcon_default as ArrowRightCircleIcon,
  ArrowRightEndOnRectangleIcon_default as ArrowRightEndOnRectangleIcon,
  ArrowRightIcon_default as ArrowRightIcon,
  ArrowRightOnRectangleIcon_default as ArrowRightOnRectangleIcon,
  ArrowRightStartOnRectangleIcon_default as ArrowRightStartOnRectangleIcon,
  ArrowSmallDownIcon_default as ArrowSmallDownIcon,
  ArrowSmallLeftIcon_default as ArrowSmallLeftIcon,
  ArrowSmallRightIcon_default as ArrowSmallRightIcon,
  ArrowSmallUpIcon_default as ArrowSmallUpIcon,
  ArrowTopRightOnSquareIcon_default as ArrowTopRightOnSquareIcon,
  ArrowTrendingDownIcon_default as ArrowTrendingDownIcon,
  ArrowTrendingUpIcon_default as ArrowTrendingUpIcon,
  ArrowTurnDownLeftIcon_default as ArrowTurnDownLeftIcon,
  ArrowTurnDownRightIcon_default as ArrowTurnDownRightIcon,
  ArrowTurnLeftDownIcon_default as ArrowTurnLeftDownIcon,
  ArrowTurnLeftUpIcon_default as ArrowTurnLeftUpIcon,
  ArrowTurnRightDownIcon_default as ArrowTurnRightDownIcon,
  ArrowTurnRightUpIcon_default as ArrowTurnRightUpIcon,
  ArrowTurnUpLeftIcon_default as ArrowTurnUpLeftIcon,
  ArrowTurnUpRightIcon_default as ArrowTurnUpRightIcon,
  ArrowUpCircleIcon_default as ArrowUpCircleIcon,
  ArrowUpIcon_default as ArrowUpIcon,
  ArrowUpLeftIcon_default as ArrowUpLeftIcon,
  ArrowUpOnSquareIcon_default as ArrowUpOnSquareIcon,
  ArrowUpOnSquareStackIcon_default as ArrowUpOnSquareStackIcon,
  ArrowUpRightIcon_default as ArrowUpRightIcon,
  ArrowUpTrayIcon_default as ArrowUpTrayIcon,
  ArrowUturnDownIcon_default as ArrowUturnDownIcon,
  ArrowUturnLeftIcon_default as ArrowUturnLeftIcon,
  ArrowUturnRightIcon_default as ArrowUturnRightIcon,
  ArrowUturnUpIcon_default as ArrowUturnUpIcon,
  ArrowsPointingInIcon_default as ArrowsPointingInIcon,
  ArrowsPointingOutIcon_default as ArrowsPointingOutIcon,
  ArrowsRightLeftIcon_default as ArrowsRightLeftIcon,
  ArrowsUpDownIcon_default as ArrowsUpDownIcon,
  AtSymbolIcon_default as AtSymbolIcon,
  BackspaceIcon_default as BackspaceIcon,
  BackwardIcon_default as BackwardIcon,
  BanknotesIcon_default as BanknotesIcon,
  Bars2Icon_default as Bars2Icon,
  Bars3BottomLeftIcon_default as Bars3BottomLeftIcon,
  Bars3BottomRightIcon_default as Bars3BottomRightIcon,
  Bars3CenterLeftIcon_default as Bars3CenterLeftIcon,
  Bars3Icon_default as Bars3Icon,
  Bars4Icon_default as Bars4Icon,
  BarsArrowDownIcon_default as BarsArrowDownIcon,
  BarsArrowUpIcon_default as BarsArrowUpIcon,
  Battery0Icon_default as Battery0Icon,
  Battery100Icon_default as Battery100Icon,
  Battery50Icon_default as Battery50Icon,
  BeakerIcon_default as BeakerIcon,
  BellAlertIcon_default as BellAlertIcon,
  BellIcon_default as BellIcon,
  BellSlashIcon_default as BellSlashIcon,
  BellSnoozeIcon_default as BellSnoozeIcon,
  BoldIcon_default as BoldIcon,
  BoltIcon_default as BoltIcon,
  BoltSlashIcon_default as BoltSlashIcon,
  BookOpenIcon_default as BookOpenIcon,
  BookmarkIcon_default as BookmarkIcon,
  BookmarkSlashIcon_default as BookmarkSlashIcon,
  BookmarkSquareIcon_default as BookmarkSquareIcon,
  BriefcaseIcon_default as BriefcaseIcon,
  BugAntIcon_default as BugAntIcon,
  BuildingLibraryIcon_default as BuildingLibraryIcon,
  BuildingOffice2Icon_default as BuildingOffice2Icon,
  BuildingOfficeIcon_default as BuildingOfficeIcon,
  BuildingStorefrontIcon_default as BuildingStorefrontIcon,
  CakeIcon_default as CakeIcon,
  CalculatorIcon_default as CalculatorIcon,
  CalendarDateRangeIcon_default as CalendarDateRangeIcon,
  CalendarDaysIcon_default as CalendarDaysIcon,
  CalendarIcon_default as CalendarIcon,
  CameraIcon_default as CameraIcon,
  ChartBarIcon_default as ChartBarIcon,
  ChartBarSquareIcon_default as ChartBarSquareIcon,
  ChartPieIcon_default as ChartPieIcon,
  ChatBubbleBottomCenterIcon_default as ChatBubbleBottomCenterIcon,
  ChatBubbleBottomCenterTextIcon_default as ChatBubbleBottomCenterTextIcon,
  ChatBubbleLeftEllipsisIcon_default as ChatBubbleLeftEllipsisIcon,
  ChatBubbleLeftIcon_default as ChatBubbleLeftIcon,
  ChatBubbleLeftRightIcon_default as ChatBubbleLeftRightIcon,
  ChatBubbleOvalLeftEllipsisIcon_default as ChatBubbleOvalLeftEllipsisIcon,
  ChatBubbleOvalLeftIcon_default as ChatBubbleOvalLeftIcon,
  CheckBadgeIcon_default as CheckBadgeIcon,
  CheckCircleIcon_default as CheckCircleIcon,
  CheckIcon_default as CheckIcon,
  ChevronDoubleDownIcon_default as ChevronDoubleDownIcon,
  ChevronDoubleLeftIcon_default as ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon_default as ChevronDoubleRightIcon,
  ChevronDoubleUpIcon_default as ChevronDoubleUpIcon,
  ChevronDownIcon_default as ChevronDownIcon,
  ChevronLeftIcon_default as ChevronLeftIcon,
  ChevronRightIcon_default as ChevronRightIcon,
  ChevronUpDownIcon_default as ChevronUpDownIcon,
  ChevronUpIcon_default as ChevronUpIcon,
  CircleStackIcon_default as CircleStackIcon,
  ClipboardDocumentCheckIcon_default as ClipboardDocumentCheckIcon,
  ClipboardDocumentIcon_default as ClipboardDocumentIcon,
  ClipboardDocumentListIcon_default as ClipboardDocumentListIcon,
  ClipboardIcon_default as ClipboardIcon,
  ClockIcon_default as ClockIcon,
  CloudArrowDownIcon_default as CloudArrowDownIcon,
  CloudArrowUpIcon_default as CloudArrowUpIcon,
  CloudIcon_default as CloudIcon,
  CodeBracketIcon_default as CodeBracketIcon,
  CodeBracketSquareIcon_default as CodeBracketSquareIcon,
  Cog6ToothIcon_default as Cog6ToothIcon,
  Cog8ToothIcon_default as Cog8ToothIcon,
  CogIcon_default as CogIcon,
  CommandLineIcon_default as CommandLineIcon,
  ComputerDesktopIcon_default as ComputerDesktopIcon,
  CpuChipIcon_default as CpuChipIcon,
  CreditCardIcon_default as CreditCardIcon,
  CubeIcon_default as CubeIcon,
  CubeTransparentIcon_default as CubeTransparentIcon,
  CurrencyBangladeshiIcon_default as CurrencyBangladeshiIcon,
  CurrencyDollarIcon_default as CurrencyDollarIcon,
  CurrencyEuroIcon_default as CurrencyEuroIcon,
  CurrencyPoundIcon_default as CurrencyPoundIcon,
  CurrencyRupeeIcon_default as CurrencyRupeeIcon,
  CurrencyYenIcon_default as CurrencyYenIcon,
  CursorArrowRaysIcon_default as CursorArrowRaysIcon,
  CursorArrowRippleIcon_default as CursorArrowRippleIcon,
  DevicePhoneMobileIcon_default as DevicePhoneMobileIcon,
  DeviceTabletIcon_default as DeviceTabletIcon,
  DivideIcon_default as DivideIcon,
  DocumentArrowDownIcon_default as DocumentArrowDownIcon,
  DocumentArrowUpIcon_default as DocumentArrowUpIcon,
  DocumentChartBarIcon_default as DocumentChartBarIcon,
  DocumentCheckIcon_default as DocumentCheckIcon,
  DocumentCurrencyBangladeshiIcon_default as DocumentCurrencyBangladeshiIcon,
  DocumentCurrencyDollarIcon_default as DocumentCurrencyDollarIcon,
  DocumentCurrencyEuroIcon_default as DocumentCurrencyEuroIcon,
  DocumentCurrencyPoundIcon_default as DocumentCurrencyPoundIcon,
  DocumentCurrencyRupeeIcon_default as DocumentCurrencyRupeeIcon,
  DocumentCurrencyYenIcon_default as DocumentCurrencyYenIcon,
  DocumentDuplicateIcon_default as DocumentDuplicateIcon,
  DocumentIcon_default as DocumentIcon,
  DocumentMagnifyingGlassIcon_default as DocumentMagnifyingGlassIcon,
  DocumentMinusIcon_default as DocumentMinusIcon,
  DocumentPlusIcon_default as DocumentPlusIcon,
  DocumentTextIcon_default as DocumentTextIcon,
  EllipsisHorizontalCircleIcon_default as EllipsisHorizontalCircleIcon,
  EllipsisHorizontalIcon_default as EllipsisHorizontalIcon,
  EllipsisVerticalIcon_default as EllipsisVerticalIcon,
  EnvelopeIcon_default as EnvelopeIcon,
  EnvelopeOpenIcon_default as EnvelopeOpenIcon,
  EqualsIcon_default as EqualsIcon,
  ExclamationCircleIcon_default as ExclamationCircleIcon,
  ExclamationTriangleIcon_default as ExclamationTriangleIcon,
  EyeDropperIcon_default as EyeDropperIcon,
  EyeIcon_default as EyeIcon,
  EyeSlashIcon_default as EyeSlashIcon,
  FaceFrownIcon_default as FaceFrownIcon,
  FaceSmileIcon_default as FaceSmileIcon,
  FilmIcon_default as FilmIcon,
  FingerPrintIcon_default as FingerPrintIcon,
  FireIcon_default as FireIcon,
  FlagIcon_default as FlagIcon,
  FolderArrowDownIcon_default as FolderArrowDownIcon,
  FolderIcon_default as FolderIcon,
  FolderMinusIcon_default as FolderMinusIcon,
  FolderOpenIcon_default as FolderOpenIcon,
  FolderPlusIcon_default as FolderPlusIcon,
  ForwardIcon_default as ForwardIcon,
  FunnelIcon_default as FunnelIcon,
  GifIcon_default as GifIcon,
  GiftIcon_default as GiftIcon,
  GiftTopIcon_default as GiftTopIcon,
  GlobeAltIcon_default as GlobeAltIcon,
  GlobeAmericasIcon_default as GlobeAmericasIcon,
  GlobeAsiaAustraliaIcon_default as GlobeAsiaAustraliaIcon,
  GlobeEuropeAfricaIcon_default as GlobeEuropeAfricaIcon,
  H1Icon_default as H1Icon,
  H2Icon_default as H2Icon,
  H3Icon_default as H3Icon,
  HandRaisedIcon_default as HandRaisedIcon,
  HandThumbDownIcon_default as HandThumbDownIcon,
  HandThumbUpIcon_default as HandThumbUpIcon,
  HashtagIcon_default as HashtagIcon,
  HeartIcon_default as HeartIcon,
  HomeIcon_default as HomeIcon,
  HomeModernIcon_default as HomeModernIcon,
  IdentificationIcon_default as IdentificationIcon,
  InboxArrowDownIcon_default as InboxArrowDownIcon,
  InboxIcon_default as InboxIcon,
  InboxStackIcon_default as InboxStackIcon,
  InformationCircleIcon_default as InformationCircleIcon,
  ItalicIcon_default as ItalicIcon,
  KeyIcon_default as KeyIcon,
  LanguageIcon_default as LanguageIcon,
  LifebuoyIcon_default as LifebuoyIcon,
  LightBulbIcon_default as LightBulbIcon,
  LinkIcon_default as LinkIcon,
  LinkSlashIcon_default as LinkSlashIcon,
  ListBulletIcon_default as ListBulletIcon,
  LockClosedIcon_default as LockClosedIcon,
  LockOpenIcon_default as LockOpenIcon,
  MagnifyingGlassCircleIcon_default as MagnifyingGlassCircleIcon,
  MagnifyingGlassIcon_default as MagnifyingGlassIcon,
  MagnifyingGlassMinusIcon_default as MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon_default as MagnifyingGlassPlusIcon,
  MapIcon_default as MapIcon,
  MapPinIcon_default as MapPinIcon,
  MegaphoneIcon_default as MegaphoneIcon,
  MicrophoneIcon_default as MicrophoneIcon,
  MinusCircleIcon_default as MinusCircleIcon,
  MinusIcon_default as MinusIcon,
  MinusSmallIcon_default as MinusSmallIcon,
  MoonIcon_default as MoonIcon,
  MusicalNoteIcon_default as MusicalNoteIcon,
  NewspaperIcon_default as NewspaperIcon,
  NoSymbolIcon_default as NoSymbolIcon,
  NumberedListIcon_default as NumberedListIcon,
  PaintBrushIcon_default as PaintBrushIcon,
  PaperAirplaneIcon_default as PaperAirplaneIcon,
  PaperClipIcon_default as PaperClipIcon,
  PauseCircleIcon_default as PauseCircleIcon,
  PauseIcon_default as PauseIcon,
  PencilIcon_default as PencilIcon,
  PencilSquareIcon_default as PencilSquareIcon,
  PercentBadgeIcon_default as PercentBadgeIcon,
  PhoneArrowDownLeftIcon_default as PhoneArrowDownLeftIcon,
  PhoneArrowUpRightIcon_default as PhoneArrowUpRightIcon,
  PhoneIcon_default as PhoneIcon,
  PhoneXMarkIcon_default as PhoneXMarkIcon,
  PhotoIcon_default as PhotoIcon,
  PlayCircleIcon_default as PlayCircleIcon,
  PlayIcon_default as PlayIcon,
  PlayPauseIcon_default as PlayPauseIcon,
  PlusCircleIcon_default as PlusCircleIcon,
  PlusIcon_default as PlusIcon,
  PlusSmallIcon_default as PlusSmallIcon,
  PowerIcon_default as PowerIcon,
  PresentationChartBarIcon_default as PresentationChartBarIcon,
  PresentationChartLineIcon_default as PresentationChartLineIcon,
  PrinterIcon_default as PrinterIcon,
  PuzzlePieceIcon_default as PuzzlePieceIcon,
  QrCodeIcon_default as QrCodeIcon,
  QuestionMarkCircleIcon_default as QuestionMarkCircleIcon,
  QueueListIcon_default as QueueListIcon,
  RadioIcon_default as RadioIcon,
  ReceiptPercentIcon_default as ReceiptPercentIcon,
  ReceiptRefundIcon_default as ReceiptRefundIcon,
  RectangleGroupIcon_default as RectangleGroupIcon,
  RectangleStackIcon_default as RectangleStackIcon,
  RocketLaunchIcon_default as RocketLaunchIcon,
  RssIcon_default as RssIcon,
  ScaleIcon_default as ScaleIcon,
  ScissorsIcon_default as ScissorsIcon,
  ServerIcon_default as ServerIcon,
  ServerStackIcon_default as ServerStackIcon,
  ShareIcon_default as ShareIcon,
  ShieldCheckIcon_default as ShieldCheckIcon,
  ShieldExclamationIcon_default as ShieldExclamationIcon,
  ShoppingBagIcon_default as ShoppingBagIcon,
  ShoppingCartIcon_default as ShoppingCartIcon,
  SignalIcon_default as SignalIcon,
  SignalSlashIcon_default as SignalSlashIcon,
  SlashIcon_default as SlashIcon,
  SparklesIcon_default as SparklesIcon,
  SpeakerWaveIcon_default as SpeakerWaveIcon,
  SpeakerXMarkIcon_default as SpeakerXMarkIcon,
  Square2StackIcon_default as Square2StackIcon,
  Square3Stack3DIcon_default as Square3Stack3DIcon,
  Squares2X2Icon_default as Squares2X2Icon,
  SquaresPlusIcon_default as SquaresPlusIcon,
  StarIcon_default as StarIcon,
  StopCircleIcon_default as StopCircleIcon,
  StopIcon_default as StopIcon,
  StrikethroughIcon_default as StrikethroughIcon,
  SunIcon_default as SunIcon,
  SwatchIcon_default as SwatchIcon,
  TableCellsIcon_default as TableCellsIcon,
  TagIcon_default as TagIcon,
  TicketIcon_default as TicketIcon,
  TrashIcon_default as TrashIcon,
  TrophyIcon_default as TrophyIcon,
  TruckIcon_default as TruckIcon,
  TvIcon_default as TvIcon,
  UnderlineIcon_default as UnderlineIcon,
  UserCircleIcon_default as UserCircleIcon,
  UserGroupIcon_default as UserGroupIcon,
  UserIcon_default as UserIcon,
  UserMinusIcon_default as UserMinusIcon,
  UserPlusIcon_default as UserPlusIcon,
  UsersIcon_default as UsersIcon,
  VariableIcon_default as VariableIcon,
  VideoCameraIcon_default as VideoCameraIcon,
  VideoCameraSlashIcon_default as VideoCameraSlashIcon,
  ViewColumnsIcon_default as ViewColumnsIcon,
  ViewfinderCircleIcon_default as ViewfinderCircleIcon,
  WalletIcon_default as WalletIcon,
  WifiIcon_default as WifiIcon,
  WindowIcon_default as WindowIcon,
  WrenchIcon_default as WrenchIcon,
  WrenchScrewdriverIcon_default as WrenchScrewdriverIcon,
  XCircleIcon_default as XCircleIcon,
  XMarkIcon_default as XMarkIcon
};
//# sourceMappingURL=@heroicons_react_20_solid.js.map
