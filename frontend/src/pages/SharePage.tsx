import { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { 
  ArrowDownTrayIcon, 
  EyeIcon, 
  LockClosedIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline'
import { publicShareApi } from '../lib/api'
import { downloadFile, isImageFile, isVideoFile, isAudioFile, isTextFile } from '../lib/utils'
import toast from 'react-hot-toast'

export default function SharePage() {
  const { shareId } = useParams<{ shareId: string }>()
  const [searchParams] = useSearchParams()
  const [password, setPassword] = useState('')
  const [shareInfo, setShareInfo] = useState<any>(null)
  const [fileUrl, setFileUrl] = useState<string | null>(null)
  const [textContent, setTextContent] = useState<string | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [needPassword, setNeedPassword] = useState(false)

  // 获取分享信息
  const getShareInfoMutation = useMutation({
    mutationFn: () => {
      console.log('🔍 获取分享信息:', shareId)
      return publicShareApi.getInfo(shareId!)
    },
    onSuccess: (response) => {
      console.log('✅ 分享信息获取成功:', response.data)
      setShareInfo(response.data)
      if (!response.data.hasPassword) {
        // 如果不需要密码，直接获取文件
        accessShareMutation.mutate()
      } else {
        setNeedPassword(true)
      }
    },
    onError: (error: any) => {
      console.error('❌ 获取分享信息失败:', error)
      console.error('错误详情:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url
      })
      toast.error('分享链接无效或已过期')
    },
  })

  // 访问分享文件
  const accessShareMutation = useMutation({
    mutationFn: () => publicShareApi.access(shareId!, password || undefined),
    onSuccess: async (response) => {
      const url = response.data.downloadUrl
      setFileUrl(url)
      setNeedPassword(false)

      // 如果是文本文件，获取文本内容用于预览
      if (shareInfo?.filename && isTextFile(shareInfo.filename)) {
        try {
          const textResponse = await fetch(url)
          if (textResponse.ok) {
            const text = await textResponse.text()
            setTextContent(text)
          }
        } catch (error) {
          console.error('获取文本内容失败:', error)
        }
      }
    },
    onError: (error: any) => {
      if (error.response?.status === 401) {
        toast.error('密码错误')
        setPassword('')
      } else {
        toast.error('访问失败')
      }
    },
  })

  useEffect(() => {
    if (shareId) {
      // 检查URL中是否有密码参数
      const urlPassword = searchParams.get('password')
      if (urlPassword) {
        setPassword(urlPassword)
      }
      getShareInfoMutation.mutate()
    }
  }, [shareId])

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (password.trim()) {
      accessShareMutation.mutate()
    }
  }

  const handleDownload = async () => {
    if (fileUrl && shareInfo?.filename) {
      try {
        await downloadFile(fileUrl, shareInfo.filename)
        toast.success('开始下载文件')
      } catch (error) {
        toast.error('下载失败')
      }
    }
  }

  const renderPreview = () => {
    if (!fileUrl || !shareInfo?.filename) return null

    const filename = shareInfo.filename

    if (isImageFile(filename)) {
      return (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg p-4">
          <img
            src={fileUrl}
            alt={filename}
            className="max-w-full max-h-96 object-contain"
            onError={() => setFileUrl(null)}
          />
        </div>
      )
    }

    if (isVideoFile(filename)) {
      return (
        <div className="bg-gray-100 rounded-lg p-4">
          <video
            src={fileUrl}
            controls
            className="w-full max-h-96"
            onError={() => setFileUrl(null)}
          >
            您的浏览器不支持视频播放
          </video>
        </div>
      )
    }

    if (isAudioFile(filename)) {
      return (
        <div className="bg-gray-100 rounded-lg p-8">
          <audio
            src={fileUrl}
            controls
            className="w-full"
            onError={() => setFileUrl(null)}
          >
            您的浏览器不支持音频播放
          </audio>
        </div>
      )
    }

    if (isTextFile(filename)) {
      return (
        <div className="bg-gray-100 rounded-lg p-4">
          {textContent ? (
            <pre className="whitespace-pre-wrap text-sm text-gray-800 max-h-96 overflow-auto font-mono">
              {textContent}
            </pre>
          ) : (
            <div className="flex items-center justify-center h-32">
              <div className="loading-spinner" />
            </div>
          )}
        </div>
      )
    }

    return (
      <div className="flex items-center justify-center h-48 bg-gray-100 rounded-lg">
        <p className="text-gray-500">无法预览此文件类型</p>
      </div>
    )
  }

  if (getShareInfoMutation.isPending) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="loading-spinner mx-auto mb-4" />
          <p className="text-gray-500">加载中...</p>
        </div>
      </div>
    )
  }

  if (getShareInfoMutation.isError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">分享链接无效</h1>
          <p className="text-gray-500">该分享链接不存在或已过期</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-primary-600 text-white p-6">
            <h1 className="text-2xl font-bold">文件分享</h1>
            {shareInfo && (
              <p className="text-primary-100 mt-2">
                文件名: {shareInfo.filename}
              </p>
            )}
          </div>

          {/* Content */}
          <div className="p-6">
            {needPassword ? (
              <div className="max-w-md mx-auto">
                <div className="text-center mb-6">
                  <LockClosedIcon className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h2 className="text-xl font-semibold text-gray-900 mb-2">
                    需要访问密码
                  </h2>
                  <p className="text-gray-500">
                    此分享文件受密码保护，请输入密码继续
                  </p>
                </div>

                <form onSubmit={handlePasswordSubmit} className="space-y-4">
                  <input
                    type="password"
                    placeholder="请输入访问密码"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="input w-full"
                    autoFocus
                  />
                  <button
                    type="submit"
                    disabled={!password.trim() || accessShareMutation.isPending}
                    className="btn-primary w-full"
                  >
                    {accessShareMutation.isPending ? '验证中...' : '访问文件'}
                  </button>
                </form>
              </div>
            ) : fileUrl ? (
              <div className="space-y-6">
                {/* Action Buttons */}
                <div className="flex items-center justify-center space-x-4">
                  <button
                    onClick={handleDownload}
                    className="btn-primary"
                  >
                    <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                    下载文件
                  </button>
                  
                  {shareInfo?.filename && (
                    isImageFile(shareInfo.filename) || 
                    isVideoFile(shareInfo.filename) || 
                    isAudioFile(shareInfo.filename) || 
                    isTextFile(shareInfo.filename)
                  ) && (
                    <button
                      onClick={() => setShowPreview(!showPreview)}
                      className="btn-secondary"
                    >
                      <EyeIcon className="h-4 w-4 mr-2" />
                      {showPreview ? '隐藏预览' : '预览文件'}
                    </button>
                  )}
                </div>

                {/* Preview */}
                {showPreview && (
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">文件预览</h3>
                    {renderPreview()}
                  </div>
                )}

                {/* Share Info */}
                {shareInfo && (
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h3 className="text-sm font-medium text-gray-900 mb-2">分享信息</h3>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>访问次数: {shareInfo.accessCount}</p>
                      {shareInfo.maxAccess && (
                        <p>最大访问次数: {shareInfo.maxAccess}</p>
                      )}
                      <p>过期时间: {new Date(shareInfo.expiresAt).toLocaleString()}</p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center">
                <div className="loading-spinner mx-auto mb-4" />
                <p className="text-gray-500">正在获取文件...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
