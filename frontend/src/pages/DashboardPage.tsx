import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'
import { bucketApi, objectApi } from '../lib/api'
import { useFileStore } from '../stores/fileStore'
import FileManager from '../components/FileManager'
import BucketOverview from '../components/BucketOverview'

export default function DashboardPage() {
  const { bucketName } = useParams()
  const { 
    currentBucket, 
    currentPrefix, 
    setCurrentBucket, 
    setObjects, 
    setFolders, 
    setLoading 
  } = useFileStore()

  // Set current bucket when URL changes
  useEffect(() => {
    if (bucketName !== currentBucket) {
      setCurrentBucket(bucketName || null)
    }
  }, [bucketName, currentBucket, setCurrentBucket])

  // Fetch bucket info
  const { data: bucketInfo, isLoading: bucketLoading } = useQuery({
    queryKey: ['bucket', currentBucket],
    queryFn: () => bucketApi.get(currentBucket!),
    enabled: !!currentBucket,
    select: (response) => response.data,
  })

  // Fetch objects in current bucket/prefix
  const { data: objectsData, isLoading: objectsLoading } = useQuery({
    queryKey: ['objects', currentBucket, currentPrefix],
    queryFn: () => objectApi.list({
      bucket: currentBucket!,
      prefix: currentPrefix || undefined,
      delimiter: '/',
    }),
    enabled: !!currentBucket,
    select: (response) => response.data,
  })

  // Update store when data changes
  useEffect(() => {
    if (objectsData) {
      setObjects(objectsData.objects || [])
      setFolders(objectsData.folders || [])
    }
  }, [objectsData, setObjects, setFolders])

  useEffect(() => {
    setLoading(objectsLoading)
  }, [objectsLoading, setLoading])

  // Show loading or bucket selector if no bucket is selected
  if (!currentBucket) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg mb-4">
          {bucketLoading ? '正在加载存储桶...' : '请选择一个存储桶开始管理文件'}
        </div>
        <div className="text-sm text-gray-400">
          {bucketLoading ? '请稍候' : '使用上方的存储桶选择器来选择要管理的存储桶'}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Bucket Overview */}
      {bucketInfo && (
        <BucketOverview 
          bucket={bucketInfo}
          isLoading={bucketLoading}
        />
      )}

      {/* File Manager */}
      <FileManager />
    </div>
  )
}
