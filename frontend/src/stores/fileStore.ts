import { create } from 'zustand'

interface FileObject {
  key: string
  size: number
  lastModified: Date
  etag: string
  storageClass?: string
}

interface Folder {
  prefix: string
  isFolder: true
}

interface FileState {
  currentBucket: string | null
  currentPrefix: string
  objects: FileObject[]
  folders: Folder[]
  selectedItems: Set<string>
  isLoading: boolean
  
  // Actions
  setCurrentBucket: (bucket: string | null) => void
  setCurrentPrefix: (prefix: string) => void
  setObjects: (objects: FileObject[]) => void
  setFolders: (folders: Folder[]) => void
  setLoading: (loading: boolean) => void
  toggleSelection: (key: string) => void
  selectAll: () => void
  clearSelection: () => void
  deleteSelected: () => void
  cleanupSelection: () => void
}

export const useFileStore = create<FileState>((set, get) => ({
  currentBucket: null,
  currentPrefix: '',
  objects: [],
  folders: [],
  selectedItems: new Set(),
  isLoading: false,

  setCurrentBucket: (bucket) => {
    set({
      currentBucket: bucket,
      currentPrefix: '',
      objects: [],
      folders: [],
      selectedItems: new Set(),
    })
  },

  setCurrentPrefix: (prefix) => {
    set({
      currentPrefix: prefix,
      selectedItems: new Set(),
    })
  },

  setObjects: (objects) => set({ objects }),
  setFolders: (folders) => set({ folders }),
  setLoading: (isLoading) => set({ isLoading }),

  toggleSelection: (key) => {
    const { selectedItems } = get()
    const newSelection = new Set(selectedItems)
    
    if (newSelection.has(key)) {
      newSelection.delete(key)
    } else {
      newSelection.add(key)
    }
    
    set({ selectedItems: newSelection })
  },

  selectAll: () => {
    const { objects } = get()
    set({ selectedItems: new Set(objects.map(obj => obj.key)) })
  },

  clearSelection: () => {
    set({ selectedItems: new Set() })
  },

  deleteSelected: () => {
    const { objects, selectedItems } = get()
    const remainingObjects = objects.filter(obj => !selectedItems.has(obj.key))
    set({
      objects: remainingObjects,
      selectedItems: new Set(),
    })
  },

  cleanupSelection: () => {
    const { objects, selectedItems } = get()
    const validKeys = new Set(objects.map(obj => obj.key))
    const cleanedSelection = new Set(
      Array.from(selectedItems).filter(key => validKeys.has(key))
    )

    // 只有在选择状态发生变化时才更新
    if (cleanedSelection.size !== selectedItems.size) {
      set({ selectedItems: cleanedSelection })
    }
  },
}))
