import { useState, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  FolderIcon,
  DocumentIcon,
  EyeIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  LinkIcon
} from '@heroicons/react/24/outline'
import { useFileStore } from '../stores/fileStore'
import { formatFileSize, formatRelativeTime, getFileIcon, canPreview, downloadFile } from '../lib/utils'
import { objectApi } from '../lib/api'
import GetLinkDialog from './GetLinkDialog'
import toast from 'react-hot-toast'

interface FileObject {
  key: string
  size: number
  lastModified: Date
  etag: string
  storageClass?: string
}

interface Folder {
  prefix: string
  isFolder: true
}

interface FileListProps {
  objects: FileObject[]
  folders: Folder[]
  onPreview: (key: string) => void
}

export default function FileList({ objects, folders, onPreview }: FileListProps) {
  const [sortBy, setSortBy] = useState<'name' | 'size' | 'modified'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [getLinkFileKey, setGetLinkFileKey] = useState<string | null>(null)

  const queryClient = useQueryClient()
  const {
    selectedItems,
    toggleSelection,
    selectAll,
    clearSelection,
    cleanupSelection,
    setCurrentPrefix,
    currentPrefix,
    currentBucket
  } = useFileStore()

  // 当文件列表变化时，清理无效的选择项
  useEffect(() => {
    cleanupSelection()
  }, [objects, cleanupSelection])

  // 下载文件
  const downloadMutation = useMutation({
    mutationFn: (fileKey: string) => objectApi.getDownloadUrl(currentBucket!, fileKey),
    onSuccess: async (response, fileKey) => {
      const fileName = fileKey.split('/').pop() || fileKey
      try {
        await downloadFile(response.data.downloadUrl, fileName)
        toast.success('开始下载文件')
      } catch (error) {
        toast.error('下载失败')
      }
    },
    onError: () => {
      toast.error('获取下载链接失败')
    },
  })

  // 删除单个文件
  const deleteMutation = useMutation({
    mutationFn: (fileKey: string) => objectApi.delete(currentBucket!, fileKey),
    onSuccess: (_, fileKey) => {
      const fileName = fileKey.split('/').pop() || fileKey
      toast.success(`文件 "${fileName}" 删除成功`)

      // 从选择状态中移除已删除的文件
      if (selectedItems.has(fileKey)) {
        toggleSelection(fileKey)
      }

      // 刷新文件列表
      queryClient.invalidateQueries({ queryKey: ['objects', currentBucket] })
    },
    onError: (error: any) => {
      console.error('删除文件失败:', error)
      toast.error('删除文件失败')
    },
  })



  const handleSort = (field: 'name' | 'size' | 'modified') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('asc')
    }
  }

  const sortedObjects = [...objects].sort((a, b) => {
    let comparison = 0
    
    switch (sortBy) {
      case 'name':
        comparison = a.key.localeCompare(b.key)
        break
      case 'size':
        comparison = a.size - b.size
        break
      case 'modified':
        comparison = new Date(a.lastModified).getTime() - new Date(b.lastModified).getTime()
        break
    }
    
    return sortOrder === 'asc' ? comparison : -comparison
  })

  const handleFolderClick = (folder: Folder) => {
    setCurrentPrefix(folder.prefix)
  }

  const handleFileAction = (action: string, fileKey: string) => {
    switch (action) {
      case 'preview':
        onPreview(fileKey)
        break
      case 'download':
        downloadMutation.mutate(fileKey)
        break
      case 'get-link':
        setGetLinkFileKey(fileKey)
        break
      case 'delete':
        const fileName = fileKey.split('/').pop() || fileKey
        const confirmed = window.confirm(`确定要删除文件 "${fileName}" 吗？`)
        if (confirmed) {
          deleteMutation.mutate(fileKey)
        }
        break
    }
  }



  const allSelected = objects.length > 0 && objects.every(obj => selectedItems.has(obj.key))
  const someSelected = objects.some(obj => selectedItems.has(obj.key))

  return (
    <div className="overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <input
              type="checkbox"
              checked={allSelected}
              ref={(input) => {
                if (input) input.indeterminate = someSelected && !allSelected
              }}
              onChange={() => allSelected ? clearSelection() : selectAll()}
              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <span className="text-sm font-medium text-gray-700">
              {folders.length + objects.length} 个项目
            </span>
          </div>
        </div>
      </div>

      {/* Table Header */}
      <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
        <div className="grid grid-cols-12 gap-2 text-xs font-medium text-gray-500 uppercase tracking-wider">
          <div className="col-span-5">
            <button
              onClick={() => handleSort('name')}
              className="flex items-center hover:text-gray-700"
            >
              名称
              {sortBy === 'name' && (
                <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
          </div>
          <div className="col-span-2">
            <button
              onClick={() => handleSort('size')}
              className="flex items-center hover:text-gray-700"
            >
              大小
              {sortBy === 'size' && (
                <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
          </div>
          <div className="col-span-2">
            <button
              onClick={() => handleSort('modified')}
              className="flex items-center hover:text-gray-700"
            >
              修改时间
              {sortBy === 'modified' && (
                <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
          </div>
          <div className="col-span-3 text-center">操作</div>
        </div>
      </div>

      {/* Content */}
      <div className="divide-y divide-gray-200">
        {/* Folders */}
        {folders.map((folder) => (
          <div
            key={folder.prefix}
            className="px-6 py-4 hover:bg-gray-50 cursor-pointer"
            onClick={() => handleFolderClick(folder)}
          >
            <div className="grid grid-cols-12 gap-2 items-center">
              <div className="col-span-5 flex items-center space-x-3">
                <FolderIcon className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium text-gray-900">
                  {folder.prefix.replace(currentPrefix, '').replace('/', '')}
                </span>
              </div>
              <div className="col-span-2 text-sm text-gray-500">-</div>
              <div className="col-span-2 text-sm text-gray-500">-</div>
              <div className="col-span-3"></div>
            </div>
          </div>
        ))}

        {/* Files */}
        {sortedObjects.map((object) => {
          const fileName = object.key.split('/').pop() || object.key
          const isSelected = selectedItems.has(object.key)
          
          return (
            <div
              key={object.key}
              className={`px-6 py-4 hover:bg-gray-50 cursor-pointer ${isSelected ? 'bg-blue-50' : ''}`}
              onDoubleClick={() => canPreview(fileName) && onPreview(object.key)}
            >
              <div className="grid grid-cols-12 gap-2 items-center">
                <div className="col-span-5 flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={isSelected}
                    onChange={(e) => {
                      e.stopPropagation()
                      toggleSelection(object.key)
                    }}
                    onClick={(e) => e.stopPropagation()}
                    className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  />
                  <span className="text-lg">{getFileIcon(fileName)}</span>
                  <span
                    className={`text-sm font-medium text-gray-900 truncate ${canPreview(fileName) ? 'hover:text-primary-600' : ''}`}
                    title={canPreview(fileName) ? '双击预览文件' : fileName}
                  >
                    {fileName}
                  </span>
                </div>

                <div className="col-span-2 text-sm text-gray-500">
                  {formatFileSize(object.size)}
                </div>

                <div className="col-span-2 text-sm text-gray-500">
                  {formatRelativeTime(object.lastModified)}
                </div>

                <div className="col-span-3">
                  <div className="flex items-center justify-center space-x-1">
                    {canPreview(fileName) && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleFileAction('preview', object.key)
                        }}
                        className="p-1 text-gray-400 hover:text-gray-600"
                        title="预览"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                    )}

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleFileAction('download', object.key)
                      }}
                      disabled={downloadMutation.isPending}
                      className="p-1 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                      title="下载"
                    >
                      <ArrowDownTrayIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleFileAction('get-link', object.key)
                      }}
                      className="p-1 text-gray-400 hover:text-blue-600"
                      title="获取链接"
                    >
                      <LinkIcon className="h-4 w-4" />
                    </button>

                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleFileAction('delete', object.key)
                      }}
                      disabled={deleteMutation.isPending}
                      className="p-1 text-gray-400 hover:text-red-600 disabled:opacity-50"
                      title="删除"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Empty State */}
      {folders.length === 0 && objects.length === 0 && (
        <div className="text-center py-12">
          <DocumentIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无文件</h3>
          <p className="mt-1 text-sm text-gray-500">开始上传文件到这个存储桶</p>
        </div>
      )}

      {/* Get Link Dialog */}
      {getLinkFileKey && (
        <GetLinkDialog
          fileKey={getLinkFileKey}
          onClose={() => setGetLinkFileKey(null)}
        />
      )}
    </div>
  )
}
