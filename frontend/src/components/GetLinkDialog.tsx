import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { XMarkIcon, ClipboardIcon, CheckIcon, LinkIcon, ShareIcon } from '@heroicons/react/24/outline'
import { objectApi, shareApi } from '../lib/api'
import { useFileStore } from '../stores/fileStore'
import { useDomainStore } from '../stores/domainStore'
import toast from 'react-hot-toast'

interface GetLinkDialogProps {
  fileKey: string
  onClose: () => void
}

type LinkType = 'direct' | 'share'

export default function GetLinkDialog({ fileKey, onClose }: GetLinkDialogProps) {
  const [linkType, setLinkType] = useState<LinkType>('direct')
  const [urlType, setUrlType] = useState<'public' | 'presigned'>('public')
  const [expiresIn, setExpiresIn] = useState('300') // 5分钟 for presigned
  const [shareExpiresIn, setShareExpiresIn] = useState('24h') // 24小时 for share
  const [maxAccess, setMaxAccess] = useState<number | undefined>()
  const [password, setPassword] = useState('')
  const [resultUrl, setResultUrl] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  
  const { currentBucket } = useFileStore()
  const { getDomain } = useDomainStore()
  const fileName = fileKey.split('/').pop() || fileKey

  // 检查当前存储桶是否配置了自定义域名
  const customDomain = currentBucket ? getDomain(currentBucket) : null

  // 直接获取文件URL
  const getDirectUrlMutation = useMutation({
    mutationFn: () => {
      if (urlType === 'public') {
        // 如果配置了自定义域名，直接构建URL
        if (customDomain) {
          const url = `${customDomain}/${fileKey}`
          return Promise.resolve({ data: { url, isPublic: true } })
        }
        return objectApi.getPublicUrl(currentBucket!, fileKey)
      } else {
        return objectApi.getDownloadUrl(currentBucket!, fileKey)
      }
    },
    onSuccess: (response) => {
      const url = response.data.url || response.data.downloadUrl
      setResultUrl(url)
      copyToClipboard(url)
    },
    onError: (error) => {
      console.error('获取URL失败:', error)
      toast.error('获取URL失败')
    },
  })

  // 创建分享链接
  const createShareMutation = useMutation({
    mutationFn: () => shareApi.create({
      bucket: currentBucket!,
      key: fileKey,
      expiresIn: shareExpiresIn,
      maxAccess: maxAccess || undefined,
      password: password || undefined,
    }),
    onSuccess: (response) => {
      // 构建前端的分享URL
      const shareUrl = `${window.location.origin}/s/${response.data.shareId}`
      setResultUrl(shareUrl)
      copyToClipboard(shareUrl)
    },
    onError: () => {
      toast.error('创建分享链接失败')
    },
  })

  const copyToClipboard = async (url: string) => {
    try {
      // 检查是否支持 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        await navigator.clipboard.writeText(url)
      } else {
        // 回退到传统方法
        const textArea = document.createElement('textarea')
        textArea.value = url
        textArea.style.position = 'fixed'
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        document.execCommand('copy')
        textArea.remove()
      }
      
      setCopied(true)
      
      if (linkType === 'direct') {
        if (urlType === 'public') {
          toast.success('文件链接已复制到剪贴板')
        } else {
          toast.success(`临时链接已复制到剪贴板（${Math.floor(parseInt(expiresIn) / 60)}分钟有效）`)
        }
      } else {
        toast.success('分享链接已复制到剪贴板')
      }
      
      setTimeout(() => {
        onClose()
      }, 1500)
    } catch (error) {
      console.error('复制失败:', error)
      toast.error('复制失败，请手动复制链接')
      prompt('请手动复制以下链接:', url)
    }
  }

  const handleGetLink = () => {
    if (linkType === 'direct') {
      getDirectUrlMutation.mutate()
    } else {
      createShareMutation.mutate()
    }
  }

  const isLoading = getDirectUrlMutation.isPending || createShareMutation.isPending

  const expirationOptions = [
    { value: '300', label: '5 分钟' },
    { value: '900', label: '15 分钟' },
    { value: '1800', label: '30 分钟' },
    { value: '3600', label: '1 小时' },
    { value: '21600', label: '6 小时' },
    { value: '86400', label: '24 小时' },
  ]

  const shareExpirationOptions = [
    { value: '1h', label: '1 小时' },
    { value: '6h', label: '6 小时' },
    { value: '24h', label: '24 小时' },
    { value: '7d', label: '7 天' },
    { value: '30d', label: '30 天' },
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">获取文件链接</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          <div>
            <p className="text-sm text-gray-600 mb-2">文件名</p>
            <p className="font-medium text-gray-900 truncate">{fileName}</p>
          </div>

          {/* Link Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              链接类型
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="direct"
                  checked={linkType === 'direct'}
                  onChange={(e) => setLinkType(e.target.value as LinkType)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <LinkIcon className="h-4 w-4 ml-2 mr-1 text-gray-500" />
                <span className="text-sm text-gray-700">
                  直接链接 (文件原始访问地址)
                </span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="share"
                  checked={linkType === 'share'}
                  onChange={(e) => setLinkType(e.target.value as LinkType)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                />
                <ShareIcon className="h-4 w-4 ml-2 mr-1 text-gray-500" />
                <span className="text-sm text-gray-700">
                  分享链接 (受控访问，支持密码保护)
                </span>
              </label>
            </div>
          </div>

          {/* Direct Link Options */}
          {linkType === 'direct' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访问方式
                </label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="public"
                      checked={urlType === 'public'}
                      onChange={(e) => setUrlType(e.target.value as 'public')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      公开链接 ({customDomain ? '使用自定义域名' : '永久有效'})
                    </span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      value="presigned"
                      checked={urlType === 'presigned'}
                      onChange={(e) => setUrlType(e.target.value as 'presigned')}
                      className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                    />
                    <span className="ml-2 text-sm text-gray-700">
                      临时链接 (有时间限制)
                    </span>
                  </label>
                </div>
              </div>

              {urlType === 'presigned' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    有效期
                  </label>
                  <select
                    value={expiresIn}
                    onChange={(e) => setExpiresIn(e.target.value)}
                    className="input"
                  >
                    {expirationOptions.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              )}
            </div>
          )}

          {/* Share Link Options */}
          {linkType === 'share' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  有效期
                </label>
                <select
                  value={shareExpiresIn}
                  onChange={(e) => setShareExpiresIn(e.target.value)}
                  className="input"
                >
                  {shareExpirationOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访问次数限制 (可选)
                </label>
                <input
                  type="number"
                  value={maxAccess || ''}
                  onChange={(e) => setMaxAccess(e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="不限制"
                  min="1"
                  className="input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  访问密码 (可选)
                </label>
                <input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="不设置密码"
                  className="input"
                />
              </div>
            </div>
          )}

          {/* Info Box */}
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">
              {linkType === 'direct' ? (
                urlType === 'public'
                  ? customDomain
                    ? `将使用自定义域名: ${customDomain}`
                    : '公开链接永久有效。如果存储桶配置了自定义域名，将使用自定义域名；否则返回临时链接。'
                  : `临时链接将在 ${expirationOptions.find(opt => opt.value === expiresIn)?.label} 后过期。`
              ) : (
                `分享链接将在 ${shareExpirationOptions.find(opt => opt.value === shareExpiresIn)?.label} 后过期。${maxAccess ? ` 最多可访问 ${maxAccess} 次。` : ''}${password ? ' 需要密码才能访问。' : ''}`
              )}
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            取消
          </button>
          
          <button
            onClick={handleGetLink}
            disabled={isLoading || copied}
            className="btn-primary"
          >
            {isLoading ? (
              <>
                <div className="loading-spinner mr-2" />
                获取中...
              </>
            ) : copied ? (
              <>
                <CheckIcon className="h-4 w-4 mr-2" />
                已复制
              </>
            ) : (
              <>
                <ClipboardIcon className="h-4 w-4 mr-2" />
                获取并复制链接
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
