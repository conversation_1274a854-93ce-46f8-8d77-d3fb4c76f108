import { ReactNode } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import {
  CloudArrowUpIcon,
  FolderIcon,
  ArrowRightOnRectangleIcon,
  UserIcon
} from '@heroicons/react/24/outline'
import { bucketApi } from '../lib/api'
import { useAuthStore } from '../stores/authStore'
import { useFileStore } from '../stores/fileStore'
import BucketSelector from './BucketSelector'

interface LayoutProps {
  children: ReactNode
}

export default function Layout({ children }: LayoutProps) {
  const navigate = useNavigate()
  const { user, logout } = useAuthStore()
  const { currentBucket } = useFileStore()

  const { data: bucketsData, isLoading: bucketsLoading } = useQuery({
    queryKey: ['buckets'],
    queryFn: () => bucketApi.list(),
    select: (response) => response.data,
    staleTime: 5 * 60 * 1000, // 5分钟内认为数据是新鲜的
    gcTime: 10 * 60 * 1000, // 10分钟缓存时间 (新版本使用 gcTime 替代 cacheTime)
    refetchOnWindowFocus: false, // 窗口聚焦时不重新获取
    refetchOnMount: false, // 组件挂载时不重新获取（如果有缓存）
  })

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/')}
                className="flex items-center space-x-3 hover:opacity-80 transition-opacity"
                title="返回主页"
              >
                <CloudArrowUpIcon className="h-8 w-8 text-primary-600" />
                <h1 className="text-xl font-semibold text-gray-900">
                  R2 文件管理器
                </h1>
              </button>
              {currentBucket && (
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <span>/</span>
                  <FolderIcon className="h-4 w-4" />
                  <span>{currentBucket}</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <BucketSelector
                buckets={(bucketsData as any)?.buckets || []}
                isLoading={bucketsLoading}
              />
              
              <div className="flex items-center space-x-2 text-sm text-gray-700">
                <UserIcon className="h-4 w-4" />
                <span>{user?.username}</span>
              </div>

              <button
                onClick={handleLogout}
                className="btn-secondary"
                title="退出登录"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {children}
      </main>
    </div>
  )
}
