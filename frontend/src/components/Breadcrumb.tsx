import { ChevronRightIcon, HomeIcon } from '@heroicons/react/20/solid'
import { useFileStore } from '../stores/fileStore'

export default function Breadcrumb() {
  const { currentBucket, currentPrefix, setCurrentPrefix } = useFileStore()

  if (!currentBucket) return null

  const pathParts = currentPrefix ? currentPrefix.split('/').filter(Boolean) : []

  const handleNavigate = (index: number) => {
    if (index === -1) {
      setCurrentPrefix('')
    } else {
      const newPrefix = pathParts.slice(0, index + 1).join('/') + '/'
      setCurrentPrefix(newPrefix)
    }
  }

  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-500">
      <button
        onClick={() => handleNavigate(-1)}
        className="flex items-center hover:text-gray-700 transition-colors"
      >
        <HomeIcon className="h-4 w-4 mr-1" />
        {currentBucket}
      </button>

      {pathParts.map((part, index) => (
        <div key={index} className="flex items-center space-x-2">
          <ChevronRightIcon className="h-4 w-4 text-gray-400" />
          <button
            onClick={() => handleNavigate(index)}
            className="hover:text-gray-700 transition-colors"
          >
            {part}
          </button>
        </div>
      ))}
    </nav>
  )
}
