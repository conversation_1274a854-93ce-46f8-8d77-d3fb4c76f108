import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import {
  CloudArrowUpIcon,
  TrashIcon,
  ShareIcon,
  FolderPlusIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'
import { useFileStore } from '../stores/fileStore'
import { objectApi } from '../lib/api'
import FileList from './FileList'
import FileUpload from './FileUpload'
import Breadcrumb from './Breadcrumb'
import FilePreview from './FilePreview'
import toast from 'react-hot-toast'

export default function FileManager() {
  const [showUpload, setShowUpload] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [previewFile, setPreviewFile] = useState<string | null>(null)

  const queryClient = useQueryClient()
  const {
    currentBucket,
    selectedItems,
    clearSelection,
    objects,
    folders,
    isLoading
  } = useFileStore()

  const selectedCount = selectedItems.size

  // 批量删除文件
  const batchDeleteMutation = useMutation({
    mutationFn: (fileKeys: string[]) => objectApi.batchDelete(currentBucket!, fileKeys),
    onSuccess: (response, fileKeys) => {
      const deletedCount = response.data.deleted?.length || 0
      const errorCount = response.data.errors?.length || 0

      if (deletedCount > 0) {
        toast.success(`成功删除 ${deletedCount} 个文件`)
      }
      if (errorCount > 0) {
        toast.error(`${errorCount} 个文件删除失败`)
      }

      clearSelection()
      // 刷新文件列表
      queryClient.invalidateQueries({ queryKey: ['objects', currentBucket] })
    },
    onError: (error: any) => {
      console.error('批量删除失败:', error)
      toast.error('批量删除失败')
    },
  })

  const handleBatchDelete = () => {
    if (selectedCount === 0) return

    const fileKeys = Array.from(selectedItems)
    const confirmed = window.confirm(`确定要删除选中的 ${selectedCount} 个文件吗？`)
    if (confirmed) {
      batchDeleteMutation.mutate(fileKeys)
    }
  }

  const handleBatchShare = () => {
    if (selectedCount === 0) return
    // TODO: Implement batch share
  }

  const filteredObjects = objects.filter(obj => 
    obj.key.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const filteredFolders = folders.filter(folder => 
    folder.prefix.toLowerCase().includes(searchQuery.toLowerCase())
  )

  if (!currentBucket) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* Toolbar */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setShowUpload(true)}
              className="btn-primary"
            >
              <CloudArrowUpIcon className="h-4 w-4 mr-2" />
              上传文件
            </button>
            
            <button className="btn-secondary">
              <FolderPlusIcon className="h-4 w-4 mr-2" />
              新建文件夹
            </button>

            {selectedCount > 0 && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={handleBatchShare}
                  className="btn-secondary"
                >
                  <ShareIcon className="h-4 w-4 mr-2" />
                  分享 ({selectedCount})
                </button>
                
                <button
                  onClick={handleBatchDelete}
                  className="btn-danger"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  删除 ({selectedCount})
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4">
            <div className="relative">
              <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索文件..."
                className="input pl-10 w-64"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Breadcrumb */}
      <Breadcrumb />

      {/* File List */}
      <div className="card">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="loading-spinner mx-auto mb-4" />
            <p className="text-gray-500">加载中...</p>
          </div>
        ) : (
          <FileList
            objects={filteredObjects}
            folders={filteredFolders}
            onPreview={setPreviewFile}
          />
        )}
      </div>

      {/* Upload Modal */}
      {showUpload && (
        <FileUpload
          onClose={() => setShowUpload(false)}
          onSuccess={() => {
            setShowUpload(false)
            // TODO: Refresh file list
          }}
        />
      )}

      {/* Preview Modal */}
      {previewFile && (
        <FilePreview
          fileKey={previewFile}
          onClose={() => setPreviewFile(null)}
        />
      )}
    </div>
  )
}
