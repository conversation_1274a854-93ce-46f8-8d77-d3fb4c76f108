import { 
  FolderIcon, 
  DocumentIcon, 
  ClockIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline'
import { formatFileSize, formatRelativeTime } from '../lib/utils'

interface BucketInfo {
  name: string
  objectCount: number
  totalSize: number
  lastModified?: Date
}

interface BucketOverviewProps {
  bucket: BucketInfo
  isLoading: boolean
}

export default function BucketOverview({ bucket, isLoading }: BucketOverviewProps) {
  if (isLoading) {
    return (
      <div className="card p-6">
        <div className="animate-pulse">
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-32"></div>
              <div className="h-3 bg-gray-200 rounded w-24"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="card p-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-4">
          <div className="p-3 bg-primary-100 rounded-lg">
            <FolderIcon className="h-6 w-6 text-primary-600" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{bucket.name}</h2>
            <p className="text-sm text-gray-500">存储桶概览</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-1 text-sm text-gray-500">
          <InformationCircleIcon className="h-4 w-4" />
          <span>Cloudflare R2</span>
        </div>
      </div>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <DocumentIcon className="h-5 w-5 text-blue-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">对象数量</p>
            <p className="text-lg font-semibold text-gray-700">
              {bucket.objectCount.toLocaleString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <div className="p-2 bg-green-100 rounded-lg">
            <FolderIcon className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">总大小</p>
            <p className="text-lg font-semibold text-gray-700">
              {formatFileSize(bucket.totalSize)}
            </p>
          </div>
        </div>

        {bucket.lastModified && (
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <ClockIcon className="h-5 w-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-900">最后修改</p>
              <p className="text-lg font-semibold text-gray-700">
                {formatRelativeTime(bucket.lastModified)}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
