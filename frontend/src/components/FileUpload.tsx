import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { useMutation } from '@tanstack/react-query'
import { XMarkIcon, CloudArrowUpIcon, DocumentIcon } from '@heroicons/react/24/outline'
import { objectApi } from '../lib/api'
import { useFileStore } from '../stores/fileStore'
import { formatFileSize } from '../lib/utils'
import toast from 'react-hot-toast'

interface FileUploadProps {
  onClose: () => void
  onSuccess: () => void
}

interface UploadFile {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

export default function FileUpload({ onClose, onSuccess }: FileUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const { currentBucket, currentPrefix } = useFileStore()

  const uploadMutation = useMutation({
    mutationFn: async (uploadFile: UploadFile) => {
      const { file } = uploadFile
      const key = currentPrefix + file.name

      // Get upload URL
      const uploadUrlResponse = await objectApi.getUploadUrl(
        currentBucket!,
        key,
        file.type
      )
      
      const { uploadUrl } = uploadUrlResponse.data

      // Upload file
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch(uploadUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      return response
    },
    onSuccess: (_, uploadFile) => {
      setUploadFiles(prev => 
        prev.map(f => 
          f.file === uploadFile.file 
            ? { ...f, status: 'success', progress: 100 }
            : f
        )
      )
      toast.success(`${uploadFile.file.name} 上传成功`)
    },
    onError: (error, uploadFile) => {
      setUploadFiles(prev => 
        prev.map(f => 
          f.file === uploadFile.file 
            ? { ...f, status: 'error', error: error.message }
            : f
        )
      )
      toast.error(`${uploadFile.file.name} 上传失败`)
    },
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newUploadFiles = acceptedFiles.map(file => ({
      file,
      progress: 0,
      status: 'pending' as const,
    }))
    
    setUploadFiles(prev => [...prev, ...newUploadFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
    maxSize: 100 * 1024 * 1024, // 100MB
  })

  const handleUpload = async () => {
    const pendingFiles = uploadFiles.filter(f => f.status === 'pending')
    
    for (const uploadFile of pendingFiles) {
      setUploadFiles(prev => 
        prev.map(f => 
          f.file === uploadFile.file 
            ? { ...f, status: 'uploading' }
            : f
        )
      )
      
      await uploadMutation.mutateAsync(uploadFile)
    }

    // Check if all uploads completed successfully
    const allSuccess = uploadFiles.every(f => f.status === 'success')
    if (allSuccess) {
      onSuccess()
    }
  }

  const removeFile = (file: File) => {
    setUploadFiles(prev => prev.filter(f => f.file !== file))
  }

  const totalFiles = uploadFiles.length
  const completedFiles = uploadFiles.filter(f => f.status === 'success').length
  const hasFiles = totalFiles > 0
  const canUpload = uploadFiles.some(f => f.status === 'pending')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">上传文件</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Drop Zone */}
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-primary-500 bg-primary-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
            <p className="mt-2 text-sm text-gray-600">
              {isDragActive
                ? '释放文件到这里'
                : '拖拽文件到这里，或点击选择文件'}
            </p>
            <p className="text-xs text-gray-500 mt-1">
              支持多文件上传，单个文件最大 100MB
            </p>
          </div>

          {/* File List */}
          {hasFiles && (
            <div className="space-y-3 max-h-60 overflow-y-auto">
              {uploadFiles.map((uploadFile, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3 flex-1">
                    <DocumentIcon className="h-5 w-5 text-gray-400" />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {uploadFile.file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(uploadFile.file.size)}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {uploadFile.status === 'uploading' && (
                      <div className="loading-spinner" />
                    )}
                    
                    {uploadFile.status === 'success' && (
                      <span className="text-green-600 text-sm">✓</span>
                    )}
                    
                    {uploadFile.status === 'error' && (
                      <span className="text-red-600 text-sm" title={uploadFile.error}>
                        ✗
                      </span>
                    )}

                    {uploadFile.status === 'pending' && (
                      <button
                        onClick={() => removeFile(uploadFile.file)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Progress */}
          {hasFiles && (
            <div className="text-sm text-gray-600">
              已完成 {completedFiles} / {totalFiles} 个文件
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="btn-secondary"
          >
            取消
          </button>
          
          <button
            onClick={handleUpload}
            disabled={!canUpload || uploadMutation.isPending}
            className="btn-primary"
          >
            {uploadMutation.isPending ? (
              <>
                <div className="loading-spinner mr-2" />
                上传中...
              </>
            ) : (
              '开始上传'
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
