import { clsx, type ClassValue } from 'clsx'
import { format, formatDistanceToNow } from 'date-fns'
import { zhCN } from 'date-fns/locale'

export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return format(d, 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })
}

export function formatRelativeTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return formatDistanceToNow(d, { addSuffix: true, locale: zhCN })
}

export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export function getFileIcon(filename: string): string {
  const ext = getFileExtension(filename)
  
  const iconMap: Record<string, string> = {
    // Images
    jpg: '🖼️', jpeg: '🖼️', png: '🖼️', gif: '🖼️', webp: '🖼️', svg: '🖼️',
    // Videos
    mp4: '🎥', webm: '🎥', avi: '🎥', mov: '🎥', mkv: '🎥',
    // Audio
    mp3: '🎵', wav: '🎵', flac: '🎵', aac: '🎵',
    // Documents
    pdf: '📄', doc: '📄', docx: '📄', txt: '📄', md: '📄',
    // Archives
    zip: '📦', rar: '📦', tar: '📦', gz: '📦',
    // Code
    js: '💻', ts: '💻', jsx: '💻', tsx: '💻', html: '💻', css: '💻',
    py: '🐍', java: '☕', cpp: '⚡', c: '⚡',
  }
  
  return iconMap[ext] || '📄'
}

export function isImageFile(filename: string): boolean {
  const ext = getFileExtension(filename)
  return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(ext)
}

export function isVideoFile(filename: string): boolean {
  const ext = getFileExtension(filename)
  return ['mp4', 'webm', 'avi', 'mov', 'mkv'].includes(ext)
}

export function isAudioFile(filename: string): boolean {
  const ext = getFileExtension(filename)
  return ['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)
}

export function isTextFile(filename: string): boolean {
  const ext = getFileExtension(filename)
  return [
    'txt', 'md', 'json', 'js', 'ts', 'jsx', 'tsx',
    'css', 'scss', 'sass', 'less', 'html', 'htm', 'xml',
    'yaml', 'yml', 'toml', 'ini', 'conf', 'config',
    'log', 'csv', 'sql', 'py', 'java', 'c', 'cpp',
    'h', 'hpp', 'cs', 'php', 'rb', 'go', 'rs', 'sh',
    'bat', 'ps1', 'dockerfile', 'gitignore', 'env'
  ].includes(ext)
}

export function canPreview(filename: string): boolean {
  return isImageFile(filename) || isVideoFile(filename) || isAudioFile(filename) || isTextFile(filename)
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + '...'
}

export async function downloadFile(url: string, filename: string): Promise<void> {
  try {
    // 首先尝试使用fetch下载，这样可以更好地处理CORS和下载
    const response = await fetch(url)

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const downloadUrl = window.URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename
    link.style.display = 'none'

    // 确保链接被添加到DOM中
    document.body.appendChild(link)

    // 触发下载
    link.click()

    // 清理
    setTimeout(() => {
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    }, 100)
  } catch (error) {
    console.error('下载失败:', error)
    // 如果fetch下载失败，回退到直接链接方式
    try {
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.target = '_blank'
      link.rel = 'noopener noreferrer'
      link.style.display = 'none'

      document.body.appendChild(link)
      link.click()

      setTimeout(() => {
        document.body.removeChild(link)
      }, 100)
    } catch (fallbackError) {
      console.error('回退下载也失败:', fallbackError)
      // 最后的回退：在新窗口打开
      window.open(url, '_blank')
    }
  }
}
