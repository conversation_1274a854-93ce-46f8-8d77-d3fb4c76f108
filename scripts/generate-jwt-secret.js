#!/usr/bin/env node

/**
 * JWT Secret Generator
 * 生成安全的 JWT 密钥
 */

const crypto = require('crypto')

function generateJWTSecret() {
  // 生成 64 字节的随机密钥
  const secret = crypto.randomBytes(64).toString('hex')
  
  console.log('🔐 生成的 JWT 密钥:')
  console.log(secret)
  console.log('')
  console.log('请将此密钥复制到 backend/.env 文件中的 JWT_SECRET 变量')
  console.log('例如:')
  console.log(`JWT_SECRET=${secret}`)
  console.log('')
  console.log('⚠️  请妥善保管此密钥，不要泄露给他人！')
  
  return secret
}

// 如果直接运行此脚本
if (require.main === module) {
  generateJWTSecret()
}

module.exports = { generateJWTSecret }
