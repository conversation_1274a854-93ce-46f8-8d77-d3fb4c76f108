import { S3Client } from '@aws-sdk/client-s3'
import { env } from './env'

export const r2Client = new S3Client({
  region: env.R2_REGION,
  endpoint: env.R2_ENDPOINT,
  credentials: {
    accessKeyId: env.R2_ACCESS_KEY_ID,
    secretAccessKey: env.R2_SECRET_ACCESS_KEY,
  },
  forcePathStyle: true,
})

export const R2_CONFIG = {
  presignedUrlExpires: env.PRESIGNED_URL_EXPIRES,
  maxFileSize: 100 * 1024 * 1024, // 100MB
  allowedMimeTypes: [
    // Images
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    // Videos
    'video/mp4',
    'video/webm',
    'video/ogg',
    // Audio
    'audio/mpeg',
    'audio/wav',
    'audio/ogg',
    // Documents
    'application/pdf',
    'text/plain',
    'text/csv',
    'application/json',
    // Archives
    'application/zip',
    'application/x-tar',
    'application/gzip',
  ],
} as const
