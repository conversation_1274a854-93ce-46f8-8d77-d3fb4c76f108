import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { jwt } from 'hono/jwt'
import { authRoutes } from './routes/auth'
import { bucketRoutes } from './routes/buckets'
import { objectRoutes } from './routes/objects'
import { shareRoutes } from './routes/share'
import { domainRoutes } from './routes/domains'
import { env } from './config/env'

const app = new Hono()

// Middleware
app.use('*', logger())
app.use('*', cors({
  origin: (origin) => {
    // Allow localhost and any IP address on port 3000
    if (!origin) return true // Allow requests with no origin (mobile apps, etc.)
    const allowedPatterns = [
      /^http:\/\/localhost:3000$/,
      /^https:\/\/localhost:3000$/,
      /^http:\/\/localhost:3001$/,
      /^https:\/\/localhost:3001$/,
      /^http:\/\/127\.0\.0\.1:3000$/,
      /^http:\/\/127\.0\.0\.1:3001$/,
      /^http:\/\/\d+\.\d+\.\d+\.\d+:300[01]$/,  // Any IP address on port 3000 or 3001
    ]
    return allowedPatterns.some(pattern => pattern.test(origin))
  },
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  credentials: true,
}))

// Public routes
app.route('/api/auth', authRoutes)
app.route('/s', shareRoutes)

// Protected routes
app.use('/api/*', jwt({
  secret: env.JWT_SECRET,
}))

app.route('/api/buckets', bucketRoutes)
app.route('/api/objects', objectRoutes)
app.route('/api/share', shareRoutes)
app.route('/api/domains', domainRoutes)

// Health check
app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() })
})

// 404 handler
app.notFound((c) => {
  return c.json({ error: 'Not Found' }, 404)
})

// Error handler
app.onError((err, c) => {
  console.error('Server error:', err)
  return c.json({ error: 'Internal Server Error' }, 500)
})

const port = env.PORT || 8000
const hostname = '0.0.0.0'
console.log(`🚀 Server running on http://${hostname}:${port}`)

export default {
  port,
  hostname,
  fetch: app.fetch,
}
