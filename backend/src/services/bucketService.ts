import { ListObjectsV2Command } from '@aws-sdk/client-s3'
import { r2Client } from '../config/r2'

export interface BucketStats {
  objectCount: number
  totalSize: number
  lastModified?: Date
}

export async function getBucketStats(bucketName: string): Promise<BucketStats> {
  try {
    let objectCount = 0
    let totalSize = 0
    let lastModified: Date | undefined
    let continuationToken: string | undefined

    do {
      const command = new ListObjectsV2Command({
        Bucket: bucketName,
        ContinuationToken: continuationToken,
        MaxKeys: 1000,
      })

      const response = await r2Client.send(command)
      
      if (response.Contents) {
        objectCount += response.Contents.length
        
        for (const object of response.Contents) {
          if (object.Size) {
            totalSize += object.Size
          }
          
          if (object.LastModified) {
            if (!lastModified || object.LastModified > lastModified) {
              lastModified = object.LastModified
            }
          }
        }
      }

      continuationToken = response.NextContinuationToken
    } while (continuationToken)

    return {
      objectCount,
      totalSize,
      lastModified,
    }
  } catch (error) {
    console.error(`Error getting stats for bucket ${bucketName}:`, error)
    return {
      objectCount: 0,
      totalSize: 0,
    }
  }
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}
