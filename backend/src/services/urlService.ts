import { GetObjectCommand } from '@aws-sdk/client-s3'
import { getSignedUrl } from '@aws-sdk/s3-request-presigner'
import { r2Client, R2_CONFIG } from '../config/r2'
import { env } from '../config/env'

export interface FileUrlOptions {
  bucket: string
  key: string
  expiresIn?: number
  forcePresigned?: boolean
}

export interface FileUrlResult {
  url: string
  isPublic: boolean
  expiresIn?: number
}

/**
 * 生成文件访问 URL
 * 如果配置了 R2_PUBLIC_URL 且适用于当前存储桶，则返回公开 URL
 * 否则返回预签名 URL
 */
export async function generateFileUrl(options: FileUrlOptions): Promise<FileUrlResult> {
  const { bucket, key, expiresIn = R2_CONFIG.presignedUrlExpires, forcePresigned = false } = options

  // 优先检查存储桶特定的自定义域名
  if (!forcePresigned) {
    const bucketDomain = getBucketCustomDomain(bucket)
    if (bucketDomain) {
      const publicUrl = `${bucketDomain}/${key}`
      return {
        url: publicUrl,
        isPublic: true,
      }
    }
  }

  // 如果配置了通用公开 URL 且不强制使用预签名 URL，且适用于当前存储桶
  if (env.R2_PUBLIC_URL && !forcePresigned && isPublicUrlApplicable(bucket)) {
    const publicUrl = buildPublicUrl(bucket, key)
    return {
      url: publicUrl,
      isPublic: true,
    }
  }

  // 生成预签名 URL
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key,
  })

  const presignedUrl = await getSignedUrl(r2Client, command, {
    expiresIn,
  })

  return {
    url: presignedUrl,
    isPublic: false,
    expiresIn,
  }
}

/**
 * 检查公开 URL 是否适用于指定存储桶
 * 如果 R2_PUBLIC_URL 包含存储桶名称，则只对该存储桶有效
 * 如果不包含存储桶名称，则对所有存储桶有效（通用域名）
 */
function isPublicUrlApplicable(bucket: string): boolean {
  if (!env.R2_PUBLIC_URL) return false

  const baseUrl = env.R2_PUBLIC_URL.replace(/\/$/, '')

  // 提取域名部分（去掉协议）
  const urlWithoutProtocol = baseUrl.replace(/^https?:\/\//, '')

  // 如果 URL 路径中包含存储桶名称，则只对该存储桶有效
  if (urlWithoutProtocol.includes('/') && urlWithoutProtocol.includes(bucket)) {
    return true
  }

  // 如果 URL 是纯域名（无路径），则认为是通用域名，对所有存储桶有效
  if (!urlWithoutProtocol.includes('/')) {
    return true
  }

  // 其他情况（URL 包含路径但不包含当前存储桶名称）则不适用
  return false
}

/**
 * 构建公开访问 URL
 */
function buildPublicUrl(bucket: string, key: string): string {
  const baseUrl = env.R2_PUBLIC_URL!.replace(/\/$/, '')

  // 提取 URL 的路径部分
  const urlWithoutProtocol = baseUrl.replace(/^https?:\/\//, '')
  const pathPart = urlWithoutProtocol.includes('/') ? urlWithoutProtocol.split('/').slice(1).join('/') : ''

  // 如果路径部分包含存储桶名称，直接拼接文件路径
  // 否则需要添加存储桶名称
  if (pathPart && pathPart.includes(bucket)) {
    return `${baseUrl}/${key}`
  } else {
    return `${baseUrl}/${bucket}/${key}`
  }
}

/**
 * 生成上传 URL（总是使用预签名 URL）
 */
export async function generateUploadUrl(bucket: string, key: string, contentType?: string): Promise<string> {
  const { PutObjectCommand } = await import('@aws-sdk/client-s3')
  
  const command = new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    ContentType: contentType,
  })

  return await getSignedUrl(r2Client, command, {
    expiresIn: R2_CONFIG.presignedUrlExpires,
  })
}

/**
 * 检查是否配置了公开访问
 */
export function hasPublicAccess(): boolean {
  return !!env.R2_PUBLIC_URL
}

/**
 * 检查指定存储桶是否支持公开访问
 */
export function hasBucketPublicAccess(bucket: string): boolean {
  return !!env.R2_PUBLIC_URL && isPublicUrlApplicable(bucket)
}

/**
 * 获取存储桶的自定义域名
 */
function getBucketCustomDomain(bucket: string): string | null {
  if (!env.R2_BUCKET_DOMAINS) {
    return null
  }

  // 解析格式: bucket1=https://domain1.com,bucket2=https://domain2.com
  const domains = env.R2_BUCKET_DOMAINS.split(',')
  for (const domain of domains) {
    const [bucketName, url] = domain.split('=')
    if (bucketName?.trim() === bucket && url?.trim()) {
      return url.trim().replace(/\/$/, '') // 移除末尾斜杠
    }
  }

  return null
}

/**
 * 获取存储桶的公开 URL 前缀
 */
export function getBucketPublicUrl(bucket: string): string | null {
  // 优先返回自定义域名
  const customDomain = getBucketCustomDomain(bucket)
  if (customDomain) {
    return customDomain
  }

  if (!env.R2_PUBLIC_URL || !isPublicUrlApplicable(bucket)) {
    return null
  }

  const baseUrl = env.R2_PUBLIC_URL.replace(/\/$/, '')

  // 提取 URL 的路径部分
  const urlWithoutProtocol = baseUrl.replace(/^https?:\/\//, '')
  const pathPart = urlWithoutProtocol.includes('/') ? urlWithoutProtocol.split('/').slice(1).join('/') : ''

  if (pathPart && pathPart.includes(bucket)) {
    return baseUrl
  } else {
    return `${baseUrl}/${bucket}`
  }
}
