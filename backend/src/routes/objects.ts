import { Hono } from 'hono'
import { 
  ListObjectsV2Command, 
  GetObjectCommand, 
  PutObjectCommand,
  DeleteObjectCommand,
  DeleteObjectsCommand,
  HeadObjectCommand 
} from '@aws-sdk/client-s3'
import { r2Client, R2_CONFIG } from '../config/r2'
import { generateFileUrl, generateUploadUrl } from '../services/urlService'
import { z } from 'zod'

const objectRoutes = new Hono()

const listObjectsSchema = z.object({
  bucket: z.string().min(1),
  prefix: z.string().optional(),
  delimiter: z.string().optional(),
  maxKeys: z.string().transform(Number).optional(),
  continuationToken: z.string().optional(),
})

const uploadUrlSchema = z.object({
  bucket: z.string().min(1),
  key: z.string().min(1),
  contentType: z.string().optional(),
})

const deleteObjectsSchema = z.object({
  bucket: z.string().min(1),
  keys: z.array(z.string().min(1)).min(1).max(1000),
})

// List objects in bucket
objectRoutes.get('/', async (c) => {
  try {
    const query = listObjectsSchema.parse(c.req.query())
    
    const command = new ListObjectsV2Command({
      Bucket: query.bucket,
      Prefix: query.prefix,
      Delimiter: query.delimiter || '/',
      MaxKeys: query.maxKeys || 1000,
      ContinuationToken: query.continuationToken,
    })

    const response = await r2Client.send(command)

    const objects = (response.Contents || []).map(obj => ({
      key: obj.Key,
      size: obj.Size,
      lastModified: obj.LastModified,
      etag: obj.ETag,
      storageClass: obj.StorageClass,
    }))

    const folders = (response.CommonPrefixes || []).map(prefix => ({
      prefix: prefix.Prefix,
      isFolder: true,
    }))

    return c.json({
      objects,
      folders,
      isTruncated: response.IsTruncated,
      nextContinuationToken: response.NextContinuationToken,
      keyCount: response.KeyCount,
    })
  } catch (error) {
    console.error('Error listing objects:', error)
    return c.json({ error: 'Failed to list objects' }, 500)
  }
})

// Get object metadata
objectRoutes.get('/:bucket/:key', async (c) => {
  try {
    const bucket = c.req.param('bucket')
    const key = decodeURIComponent(c.req.param('key'))

    const command = new HeadObjectCommand({ Bucket: bucket, Key: key })
    const response = await r2Client.send(command)

    return c.json({
      key,
      size: response.ContentLength,
      lastModified: response.LastModified,
      contentType: response.ContentType,
      etag: response.ETag,
      metadata: response.Metadata,
    })
  } catch (error: any) {
    console.error('Error getting object metadata:', error)
    
    if (error.name === 'NotFound') {
      return c.json({ error: 'Object not found' }, 404)
    }
    
    return c.json({ error: 'Failed to get object metadata' }, 500)
  }
})

// Generate presigned URL for upload
objectRoutes.post('/upload-url', async (c) => {
  try {
    const body = await c.req.json()
    const { bucket, key, contentType } = uploadUrlSchema.parse(body)

    // Validate content type
    if (contentType && !R2_CONFIG.allowedMimeTypes.includes(contentType as any)) {
      return c.json({ error: 'File type not allowed' }, 400)
    }

    const uploadUrl = await generateUploadUrl(bucket, key, contentType)

    return c.json({
      uploadUrl,
      key,
      expiresIn: R2_CONFIG.presignedUrlExpires,
    })
  } catch (error) {
    console.error('Error generating upload URL:', error)
    return c.json({ error: 'Failed to generate upload URL' }, 500)
  }
})

// Generate URL for download/preview
objectRoutes.post('/download-url', async (c) => {
  try {
    const body = await c.req.json()
    const { bucket, key } = z.object({
      bucket: z.string().min(1),
      key: z.string().min(1),
    }).parse(body)

    const urlResult = await generateFileUrl({ bucket, key })

    return c.json({
      downloadUrl: urlResult.url,
      isPublic: urlResult.isPublic,
      expiresIn: urlResult.expiresIn,
    })
  } catch (error) {
    console.error('Error generating download URL:', error)
    return c.json({ error: 'Failed to generate download URL' }, 500)
  }
})

// Generate public URL (for copying)
objectRoutes.post('/public-url', async (c) => {
  try {
    const body = await c.req.json()
    const { bucket, key } = z.object({
      bucket: z.string().min(1),
      key: z.string().min(1),
    }).parse(body)

    // 优先返回公开URL，如果没有配置则返回预签名URL
    const urlResult = await generateFileUrl({
      bucket,
      key,
      forcePresigned: false  // 优先使用公开URL
    })

    return c.json({
      url: urlResult.url,
      isPublic: urlResult.isPublic,
      expiresIn: urlResult.expiresIn,
    })
  } catch (error) {
    console.error('Error generating public URL:', error)
    return c.json({ error: 'Failed to generate public URL' }, 500)
  }
})

// Delete single object
objectRoutes.delete('/:bucket/:key', async (c) => {
  try {
    const bucket = c.req.param('bucket')
    const key = decodeURIComponent(c.req.param('key'))

    const command = new DeleteObjectCommand({ Bucket: bucket, Key: key })
    await r2Client.send(command)

    return c.json({ message: 'Object deleted successfully' })
  } catch (error) {
    console.error('Error deleting object:', error)
    return c.json({ error: 'Failed to delete object' }, 500)
  }
})

// Batch delete objects
objectRoutes.post('/delete', async (c) => {
  try {
    const body = await c.req.json()
    const { bucket, keys } = deleteObjectsSchema.parse(body)

    const command = new DeleteObjectsCommand({
      Bucket: bucket,
      Delete: {
        Objects: keys.map(key => ({ Key: key })),
        Quiet: false,
      },
    })

    const response = await r2Client.send(command)

    return c.json({
      deleted: response.Deleted || [],
      errors: response.Errors || [],
    })
  } catch (error) {
    console.error('Error batch deleting objects:', error)
    return c.json({ error: 'Failed to delete objects' }, 500)
  }
})

export { objectRoutes }
