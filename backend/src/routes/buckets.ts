import { Hono } from 'hono'
import { ListBucketsCommand, HeadBucketCommand } from '@aws-sdk/client-s3'
import { r2Client } from '../config/r2'
import { getBucketStats } from '../services/bucketService'

const bucketRoutes = new Hono()

// List all buckets
bucketRoutes.get('/', async (c) => {
  try {
    const command = new ListBucketsCommand({})
    const response = await r2Client.send(command)

    const buckets = await Promise.all(
      (response.Buckets || []).map(async (bucket) => {
        const stats = await getBucketStats(bucket.Name!)
        return {
          name: bucket.Name,
          creationDate: bucket.CreationDate,
          ...stats,
        }
      })
    )

    return c.json({
      buckets,
      total: buckets.length,
    })
  } catch (error) {
    console.error('Error listing buckets:', error)
    return c.json({ error: 'Failed to list buckets' }, 500)
  }
})

// Get bucket info
bucketRoutes.get('/:bucketName', async (c) => {
  try {
    const bucketName = c.req.param('bucketName')
    
    // Check if bucket exists
    const headCommand = new HeadBucketCommand({ Bucket: bucketName })
    await r2Client.send(headCommand)

    // Get bucket statistics
    const stats = await getBucketStats(bucketName)

    return c.json({
      name: bucketName,
      ...stats,
    })
  } catch (error: any) {
    console.error('Error getting bucket info:', error)
    
    if (error.name === 'NotFound') {
      return c.json({ error: 'Bucket not found' }, 404)
    }
    
    return c.json({ error: 'Failed to get bucket info' }, 500)
  }
})

// Check bucket access (HEAD requests are automatically handled by GET)
bucketRoutes.get('/:bucketName/check', async (c) => {
  try {
    const bucketName = c.req.param('bucketName')
    const command = new HeadBucketCommand({ Bucket: bucketName })
    await r2Client.send(command)

    return c.json({ exists: true, bucket: bucketName })
  } catch (error: any) {
    if (error.name === 'NotFound') {
      return c.json({ exists: false, bucket: bucketName }, 404)
    }
    return c.json({ error: 'Failed to check bucket' }, 500)
  }
})

export { bucketRoutes }
