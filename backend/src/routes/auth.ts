import { Hono } from 'hono'
import { sign } from 'jsonwebtoken'
import { z } from 'zod'
import { env } from '../config/env'

const authRoutes = new Hono()

const loginSchema = z.object({
  username: z.string().min(1).max(50),
  password: z.string().min(1).max(100),
})

// Get admin info
authRoutes.get('/info', async (c) => {
  return c.json({
    adminConfigured: !!(env.ADMIN_USERNAME && env.ADMIN_PASSWORD),
    username: env.ADMIN_USERNAME || null
  })
})

// Login endpoint - uses environment variables for admin credentials
authRoutes.post('/login', async (c) => {
  try {
    const body = await c.req.json()
    const { username, password } = loginSchema.parse(body)

    // Check against environment variables
    if (username !== env.ADMIN_USERNAME || password !== env.ADMIN_PASSWORD) {
      return c.json({ error: 'Invalid credentials' }, 401)
    }

    const userId = 'admin'
    const token = sign(
      { userId, username },
      env.JWT_SECRET,
      { expiresIn: env.JWT_EXPIRES_IN }
    )

    return c.json({
      message: 'Login successful',
      token,
      user: { id: userId, username },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return c.json({ error: 'Invalid input', details: error.errors }, 400)
    }
    console.error('Login error:', error)
    return c.json({ error: 'Internal server error' }, 500)
  }
})

// Verify token endpoint
authRoutes.get('/verify', async (c) => {
  const authHeader = c.req.header('Authorization')
  if (!authHeader?.startsWith('Bearer ')) {
    return c.json({ error: 'No token provided' }, 401)
  }

  try {
    const token = authHeader.substring(7)
    const jwt = await import('jsonwebtoken')
    const decoded = jwt.verify(token, env.JWT_SECRET) as any

    return c.json({
      valid: true,
      user: { id: decoded.userId, username: decoded.username },
    })
  } catch (error) {
    return c.json({ error: 'Invalid token' }, 401)
  }
})

export { authRoutes }
