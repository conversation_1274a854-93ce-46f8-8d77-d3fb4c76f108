import { describe, it, expect, beforeAll } from 'bun:test'

describe('Auth API', () => {
  const baseUrl = 'http://localhost:8000'
  
  it('should register a new user', async () => {
    const response = await fetch(`${baseUrl}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'testpassword123',
      }),
    })

    expect(response.status).toBe(200)
    
    const data = await response.json()
    expect(data.token).toBeDefined()
    expect(data.user.username).toBe('testuser')
  })

  it('should login with valid credentials', async () => {
    const response = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'testpassword123',
      }),
    })

    expect(response.status).toBe(200)
    
    const data = await response.json()
    expect(data.token).toBeDefined()
    expect(data.user.username).toBe('testuser')
  })

  it('should reject invalid credentials', async () => {
    const response = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'wrongpassword',
      }),
    })

    expect(response.status).toBe(401)
  })

  it('should verify valid token', async () => {
    // First login to get a token
    const loginResponse = await fetch(`${baseUrl}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'testuser',
        password: 'testpassword123',
      }),
    })

    const loginData = await loginResponse.json()
    const token = loginData.token

    // Then verify the token
    const verifyResponse = await fetch(`${baseUrl}/api/auth/verify`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    })

    expect(verifyResponse.status).toBe(200)
    
    const verifyData = await verifyResponse.json()
    expect(verifyData.valid).toBe(true)
    expect(verifyData.user.username).toBe('testuser')
  })
})
